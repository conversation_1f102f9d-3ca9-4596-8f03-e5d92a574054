Contributing
============

Hybridauth is a community driven project, and it needs your help to keep the project going.


### Report Problems

A great way to help is to find and submit [bug reports](https://github.com/hybridauth/hybridauth/issues) or to fix the
existing ones. 

When reporting new issues, please provide as much detail and context as possible, otherwise we will be left in the dark
about the problem you having.


### Documentation

You can help improve Hybridauth user documentation (this website) by making it more consistent and readable, and by adding
missing information, correcting errors and typos.

Hybridauth documentation is maintained as plain text markdown files at https://github.com/hybridauth/hybridauth.github.io


### Develop

If you have fixed a bug or implemented a new feature that you would like to share with the community, send a pull request
against the [dev branch](https://github.com/hybridauth/hybridauth/).

Before contributing code, please consider these guide lines:

**Coding Style**

Hybridauth follows [PSR-1](http://www.php-fig.org/psr/psr-1/) and [PSR-2](http://www.php-fig.org/psr/psr-2/).

Please prevent your IDE for reformatting huge chunks of code/files as it make it nearly impossible to see what changes were
actually made to a file on your Pull Request.

**Compatibility**

Additional providers, minor enhancements, bugs and typos fixes are most welcome. Large and "breaking" changes should be 
discussed ahead of time. **Please ask first**.

Hybridauth 3 is compatible with **PHP 5.4** and therefore all code supplied must stick to this requirement.

**License**

Hybridauth PHP Library is released under the terms of MIT License: By contributing your code to the project, you agree to
license your contribution under the MIT License. (which means, once you donate your code to the community, it become freely
available to everyone to use, or mis-use).
