/*! Select2 4.1.0-rc.0 | https://github.com/select2/select2/blob/master/LICENSE.md */
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=function(t,n){return void 0===n&&(n="undefined"!=typeof window?require("jquery"):require("jquery")(t)),e(n),n}:e(jQuery)}((function(e){var t,n,s,i,r,o,a,l,c,u,d,p,h,f,g;function m(e,t){return p.call(e,t)}function y(e,t){var n,s,i,r,o,a,l,c,d,p,h=t&&t.split("/"),g=u.map,m=g&&g["*"]||{};if(e){for(t=(e=e.split("/")).length-1,u.nodeIdCompat&&f.test(e[t])&&(e[t]=e[t].replace(f,"")),"."===e[0].charAt(0)&&h&&(e=h.slice(0,h.length-1).concat(e)),c=0;c<e.length;c++)"."===(p=e[c])?(e.splice(c,1),--c):".."===p&&(0===c||1===c&&".."===e[2]||".."===e[c-1]||0<c&&(e.splice(c-1,2),c-=2));e=e.join("/")}if((h||m)&&g){for(c=(n=e.split("/")).length;0<c;--c){if(s=n.slice(0,c).join("/"),h)for(d=h.length;0<d;--d)if(i=(i=g[h.slice(0,d).join("/")])&&i[s]){r=i,o=c;break}if(r)break;!a&&m&&m[s]&&(a=m[s],l=c)}!r&&a&&(r=a,o=l),r&&(n.splice(0,o,r),e=n.join("/"))}return e}function v(e,t){return function(){var n=h.call(arguments,0);return"string"!=typeof n[0]&&1===n.length&&n.push(null),r.apply(s,n.concat([e,t]))}}function _(e){var t;if(m(c,e)&&(t=c[e],delete c[e],d[e]=!0,i.apply(s,t)),!m(l,e)&&!m(d,e))throw new Error("No "+e);return l[e]}function b(e){var t,n=e?e.indexOf("!"):-1;return-1<n&&(t=e.substring(0,n),e=e.substring(n+1,e.length)),[t,e]}function w(e){return e?b(e):[]}var x=(g=((x=e&&e.fn&&e.fn.select2&&e.fn.select2.amd?e.fn.select2.amd:x)&&x.requirejs||(x?n=x:x={},l={},c={},u={},d={},p=Object.prototype.hasOwnProperty,h=[].slice,f=/\.js$/,o=function(e,t){var n,s,i=b(e),r=i[0];t=t[1];return e=i[1],r&&(n=_(r=y(r,t))),r?e=n&&n.normalize?n.normalize(e,(s=t,function(e){return y(e,s)})):y(e,t):(r=(i=b(e=y(e,t)))[0],e=i[1],r&&(n=_(r))),{f:r?r+"!"+e:e,n:e,pr:r,p:n}},a={require:function(e){return v(e)},exports:function(e){var t=l[e];return void 0!==t?t:l[e]={}},module:function(e){return{id:e,uri:"",exports:l[e],config:(t=e,function(){return u&&u.config&&u.config[t]||{}})};var t}},i=function(e,t,n,i){var r,u,p,h,f,g=[],y=typeof n,b=w(i=i||e);if("undefined"==y||"function"==y){for(t=!t.length&&n.length?["require","exports","module"]:t,h=0;h<t.length;h+=1)if("require"===(u=(p=o(t[h],b)).f))g[h]=a.require(e);else if("exports"===u)g[h]=a.exports(e),f=!0;else if("module"===u)r=g[h]=a.module(e);else if(m(l,u)||m(c,u)||m(d,u))g[h]=_(u);else{if(!p.p)throw new Error(e+" missing "+u);p.p.load(p.n,v(i,!0),function(e){return function(t){l[e]=t}}(u),{}),g[h]=l[u]}y=n?n.apply(l[e],g):void 0,e&&(r&&r.exports!==s&&r.exports!==l[e]?l[e]=r.exports:y===s&&f||(l[e]=y))}else e&&(l[e]=n)},t=n=r=function(e,t,n,l,c){if("string"==typeof e)return a[e]?a[e](t):_(o(e,w(t)).f);if(!e.splice){if((u=e).deps&&r(u.deps,u.callback),!t)return;t.splice?(e=t,t=n,n=null):e=s}return t=t||function(){},"function"==typeof n&&(n=l,l=c),l?i(s,e,t,n):setTimeout((function(){i(s,e,t,n)}),4),r},r.config=function(e){return r(e)},t._defined=l,(g=function(e,t,n){if("string"!=typeof e)throw new Error("See almond README: incorrect module build, no module name");t.splice||(n=t,t=[]),m(l,e)||m(c,e)||(c[e]=[e,t,n])}).amd={jQuery:!0},x.requirejs=t,x.require=n,x.define=g),x.define("almond",(function(){})),x.define("jquery",[],(function(){var t=e||$;return null==t&&console&&console.error&&console.error("Select2: An instance of jQuery or a jQuery-compatible library was not found. Make sure that you are including jQuery before Select2 on your web page."),t})),x.define("select2/utils",["jquery"],(function(e){var t={};function n(e){var t,n=e.prototype,s=[];for(t in n)"function"==typeof n[t]&&"constructor"!==t&&s.push(t);return s}function s(){this.listeners={}}t.Extend=function(e,t){var n,s={}.hasOwnProperty;function i(){this.constructor=e}for(n in t)s.call(t,n)&&(e[n]=t[n]);return i.prototype=t.prototype,e.prototype=new i,e.__super__=t.prototype,e},t.Decorate=function(e,t){var s=n(t),i=n(e);function r(){var n=Array.prototype.unshift,s=t.prototype.constructor.length,i=e.prototype.constructor;0<s&&(n.call(arguments,e.prototype.constructor),i=t.prototype.constructor),i.apply(this,arguments)}t.displayName=e.displayName,r.prototype=new function(){this.constructor=r};for(var o=0;o<i.length;o++){var a=i[o];r.prototype[a]=e.prototype[a]}for(var l=0;l<s.length;l++){var c=s[l];r.prototype[c]=function(e){var n=function(){};e in r.prototype&&(n=r.prototype[e]);var s=t.prototype[e];return function(){return Array.prototype.unshift.call(arguments,n),s.apply(this,arguments)}}(c)}return r},s.prototype.on=function(e,t){this.listeners=this.listeners||{},e in this.listeners?this.listeners[e].push(t):this.listeners[e]=[t]},s.prototype.trigger=function(e){var t=Array.prototype.slice,n=t.call(arguments,1);this.listeners=this.listeners||{},0===(n=null==n?[]:n).length&&n.push({}),(n[0]._type=e)in this.listeners&&this.invoke(this.listeners[e],t.call(arguments,1)),"*"in this.listeners&&this.invoke(this.listeners["*"],arguments)},s.prototype.invoke=function(e,t){for(var n=0,s=e.length;n<s;n++)e[n].apply(this,t)},t.Observable=s,t.generateChars=function(e){for(var t="",n=0;n<e;n++)t+=Math.floor(36*Math.random()).toString(36);return t},t.bind=function(e,t){return function(){e.apply(t,arguments)}},t._convertData=function(e){for(var t in e){var n=t.split("-"),s=e;if(1!==n.length){for(var i=0;i<n.length;i++){var r=n[i];(r=r.substring(0,1).toLowerCase()+r.substring(1))in s||(s[r]={}),i==n.length-1&&(s[r]=e[t]),s=s[r]}delete e[t]}}return e},t.hasScroll=function(t,n){var s=e(n),i=n.style.overflowX,r=n.style.overflowY;return(i!==r||"hidden"!==r&&"visible"!==r)&&("scroll"===i||"scroll"===r||s.innerHeight()<n.scrollHeight||s.innerWidth()<n.scrollWidth)},t.escapeMarkup=function(e){var t={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return"string"!=typeof e?e:String(e).replace(/[&<>"'\/\\]/g,(function(e){return t[e]}))},t.__cache={};var i=0;return t.GetUniqueElementId=function(e){var n=e.getAttribute("data-select2-id");return null!=n||(n=e.id?"select2-data-"+e.id:"select2-data-"+(++i).toString()+"-"+t.generateChars(4),e.setAttribute("data-select2-id",n)),n},t.StoreData=function(e,n,s){e=t.GetUniqueElementId(e),t.__cache[e]||(t.__cache[e]={}),t.__cache[e][n]=s},t.GetData=function(n,s){var i=t.GetUniqueElementId(n);return s?t.__cache[i]&&null!=t.__cache[i][s]?t.__cache[i][s]:e(n).data(s):t.__cache[i]},t.RemoveData=function(e){var n=t.GetUniqueElementId(e);null!=t.__cache[n]&&delete t.__cache[n],e.removeAttribute("data-select2-id")},t.copyNonInternalCssClasses=function(e,t){var n=(n=e.getAttribute("class").trim().split(/\s+/)).filter((function(e){return 0===e.indexOf("select2-")}));t=(t=t.getAttribute("class").trim().split(/\s+/)).filter((function(e){return 0!==e.indexOf("select2-")})),t=n.concat(t);e.setAttribute("class",t.join(" "))},t})),x.define("select2/results",["jquery","./utils"],(function(e,t){function n(e,t,s){this.$element=e,this.data=s,this.options=t,n.__super__.constructor.call(this)}return t.Extend(n,t.Observable),n.prototype.render=function(){var t=e('<ul class="select2-results__options" role="listbox"></ul>');return this.options.get("multiple")&&t.attr("aria-multiselectable","true"),this.$results=t},n.prototype.clear=function(){this.$results.empty()},n.prototype.displayMessage=function(t){var n=this.options.get("escapeMarkup");this.clear(),this.hideLoading();var s=e('<li role="alert" aria-live="assertive" class="select2-results__option"></li>'),i=this.options.get("translations").get(t.message);s.append(n(i(t.args))),s[0].className+=" select2-results__message",this.$results.append(s)},n.prototype.hideMessages=function(){this.$results.find(".select2-results__message").remove()},n.prototype.append=function(e){this.hideLoading();var t=[];if(null!=e.results&&0!==e.results.length){e.results=this.sort(e.results);for(var n=0;n<e.results.length;n++){var s=e.results[n];s=this.option(s);t.push(s)}this.$results.append(t)}else 0===this.$results.children().length&&this.trigger("results:message",{message:"noResults"})},n.prototype.position=function(e,t){t.find(".select2-results").append(e)},n.prototype.sort=function(e){return this.options.get("sorter")(e)},n.prototype.highlightFirstItem=function(){var e=this.$results.find(".select2-results__option--selectable"),t=e.filter(".select2-results__option--selected");(0<t.length?t:e).first().trigger("mouseenter"),this.ensureHighlightVisible()},n.prototype.setClasses=function(){var n=this;this.data.current((function(s){var i=s.map((function(e){return e.id.toString()}));n.$results.find(".select2-results__option--selectable").each((function(){var n=e(this),s=t.GetData(this,"data"),r=""+s.id;null!=s.element&&s.element.selected||null==s.element&&-1<i.indexOf(r)?(this.classList.add("select2-results__option--selected"),n.attr("aria-selected","true")):(this.classList.remove("select2-results__option--selected"),n.attr("aria-selected","false"))}))}))},n.prototype.showLoading=function(e){this.hideLoading(),e={disabled:!0,loading:!0,text:this.options.get("translations").get("searching")(e)},(e=this.option(e)).className+=" loading-results",this.$results.prepend(e)},n.prototype.hideLoading=function(){this.$results.find(".loading-results").remove()},n.prototype.option=function(n){var s=document.createElement("li");s.classList.add("select2-results__option"),s.classList.add("select2-results__option--selectable");var i,r={role:"option"},o=window.Element.prototype.matches||window.Element.prototype.msMatchesSelector||window.Element.prototype.webkitMatchesSelector;for(i in(null!=n.element&&o.call(n.element,":disabled")||null==n.element&&n.disabled)&&(r["aria-disabled"]="true",s.classList.remove("select2-results__option--selectable"),s.classList.add("select2-results__option--disabled")),null==n.id&&s.classList.remove("select2-results__option--selectable"),null!=n._resultId&&(s.id=n._resultId),n.title&&(s.title=n.title),n.children&&(r.role="group",r["aria-label"]=n.text,s.classList.remove("select2-results__option--selectable"),s.classList.add("select2-results__option--group")),r){var a=r[i];s.setAttribute(i,a)}if(n.children){var l=e(s),c=document.createElement("strong");c.className="select2-results__group",this.template(n,c);for(var u=[],d=0;d<n.children.length;d++){var p=n.children[d];p=this.option(p);u.push(p)}(o=e("<ul></ul>",{"class":"select2-results__options select2-results__options--nested",role:"none"})).append(u),l.append(c),l.append(o)}else this.template(n,s);return t.StoreData(s,"data",n),s},n.prototype.bind=function(n,s){var i=this,r=n.id+"-results";this.$results.attr("id",r),n.on("results:all",(function(e){i.clear(),i.append(e.data),n.isOpen()&&(i.setClasses(),i.highlightFirstItem())})),n.on("results:append",(function(e){i.append(e.data),n.isOpen()&&i.setClasses()})),n.on("query",(function(e){i.hideMessages(),i.showLoading(e)})),n.on("select",(function(){n.isOpen()&&(i.setClasses(),i.options.get("scrollAfterSelect")&&i.highlightFirstItem())})),n.on("unselect",(function(){n.isOpen()&&(i.setClasses(),i.options.get("scrollAfterSelect")&&i.highlightFirstItem())})),n.on("open",(function(){i.$results.attr("aria-expanded","true"),i.$results.attr("aria-hidden","false"),i.setClasses(),i.ensureHighlightVisible()})),n.on("close",(function(){i.$results.attr("aria-expanded","false"),i.$results.attr("aria-hidden","true"),i.$results.removeAttr("aria-activedescendant")})),n.on("results:toggle",(function(){var e=i.getHighlightedResults();0!==e.length&&e.trigger("mouseup")})),n.on("results:select",(function(){var e,n=i.getHighlightedResults();0!==n.length&&(e=t.GetData(n[0],"data"),n.hasClass("select2-results__option--selected")?i.trigger("close",{}):i.trigger("select",{data:e}))})),n.on("results:previous",(function(){var e,t=i.getHighlightedResults(),n=i.$results.find(".select2-results__option--selectable"),s=n.index(t);s<=0||(e=s-1,0===t.length&&(e=0),(s=n.eq(e)).trigger("mouseenter"),t=i.$results.offset().top,n=s.offset().top,s=i.$results.scrollTop()+(n-t),0===e?i.$results.scrollTop(0):n-t<0&&i.$results.scrollTop(s))})),n.on("results:next",(function(){var e,t=i.getHighlightedResults(),n=i.$results.find(".select2-results__option--selectable"),s=n.index(t)+1;s>=n.length||((e=n.eq(s)).trigger("mouseenter"),t=i.$results.offset().top+i.$results.outerHeight(!1),n=e.offset().top+e.outerHeight(!1),e=i.$results.scrollTop()+n-t,0===s?i.$results.scrollTop(0):t<n&&i.$results.scrollTop(e))})),n.on("results:focus",(function(e){e.element[0].classList.add("select2-results__option--highlighted"),e.element[0].setAttribute("aria-selected","true")})),n.on("results:message",(function(e){i.displayMessage(e)})),e.fn.mousewheel&&this.$results.on("mousewheel",(function(e){var t=i.$results.scrollTop(),n=i.$results.get(0).scrollHeight-t+e.deltaY;t=0<e.deltaY&&t-e.deltaY<=0,n=e.deltaY<0&&n<=i.$results.height();t?(i.$results.scrollTop(0),e.preventDefault(),e.stopPropagation()):n&&(i.$results.scrollTop(i.$results.get(0).scrollHeight-i.$results.height()),e.preventDefault(),e.stopPropagation())})),this.$results.on("mouseup",".select2-results__option--selectable",(function(n){var s=e(this),r=t.GetData(this,"data");s.hasClass("select2-results__option--selected")?i.options.get("multiple")?i.trigger("unselect",{originalEvent:n,data:r}):i.trigger("close",{}):i.trigger("select",{originalEvent:n,data:r})})),this.$results.on("mouseenter",".select2-results__option--selectable",(function(n){var s=t.GetData(this,"data");i.getHighlightedResults().removeClass("select2-results__option--highlighted").attr("aria-selected","false"),i.trigger("results:focus",{data:s,element:e(this)})}))},n.prototype.getHighlightedResults=function(){return this.$results.find(".select2-results__option--highlighted")},n.prototype.destroy=function(){this.$results.remove()},n.prototype.ensureHighlightVisible=function(){var e,t,n,s,i=this.getHighlightedResults();0!==i.length&&(e=this.$results.find(".select2-results__option--selectable").index(i),s=this.$results.offset().top,t=i.offset().top,n=this.$results.scrollTop()+(t-s),s=t-s,n-=2*i.outerHeight(!1),e<=2?this.$results.scrollTop(0):(s>this.$results.outerHeight()||s<0)&&this.$results.scrollTop(n))},n.prototype.template=function(t,n){var s=this.options.get("templateResult"),i=this.options.get("escapeMarkup");null==(t=s(t,n))?n.style.display="none":"string"==typeof t?n.innerHTML=i(t):e(n).append(t)},n})),x.define("select2/keys",[],(function(){return{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46}})),x.define("select2/selection/base",["jquery","../utils","../keys"],(function(e,t,n){function s(e,t){this.$element=e,this.options=t,s.__super__.constructor.call(this)}return t.Extend(s,t.Observable),s.prototype.render=function(){var n=e('<span class="select2-selection" role="combobox"  aria-haspopup="true" aria-expanded="false"></span>');return this._tabindex=0,null!=t.GetData(this.$element[0],"old-tabindex")?this._tabindex=t.GetData(this.$element[0],"old-tabindex"):null!=this.$element.attr("tabindex")&&(this._tabindex=this.$element.attr("tabindex")),n.attr("title",this.$element.attr("title")),n.attr("tabindex",this._tabindex),n.attr("aria-disabled","false"),this.$selection=n},s.prototype.bind=function(e,t){var s=this,i=e.id+"-results";this.container=e,this.$selection.on("focus",(function(e){s.trigger("focus",e)})),this.$selection.on("blur",(function(e){s._handleBlur(e)})),this.$selection.on("keydown",(function(e){s.trigger("keypress",e),e.which===n.SPACE&&e.preventDefault()})),e.on("results:focus",(function(e){s.$selection.attr("aria-activedescendant",e.data._resultId)})),e.on("selection:update",(function(e){s.update(e.data)})),e.on("open",(function(){s.$selection.attr("aria-expanded","true"),s.$selection.attr("aria-owns",i),s._attachCloseHandler(e)})),e.on("close",(function(){s.$selection.attr("aria-expanded","false"),s.$selection.removeAttr("aria-activedescendant"),s.$selection.removeAttr("aria-owns"),s.$selection.trigger("focus"),s._detachCloseHandler(e)})),e.on("enable",(function(){s.$selection.attr("tabindex",s._tabindex),s.$selection.attr("aria-disabled","false")})),e.on("disable",(function(){s.$selection.attr("tabindex","-1"),s.$selection.attr("aria-disabled","true")}))},s.prototype._handleBlur=function(t){var n=this;window.setTimeout((function(){document.activeElement==n.$selection[0]||e.contains(n.$selection[0],document.activeElement)||n.trigger("blur",t)}),1)},s.prototype._attachCloseHandler=function(n){e(document.body).on("mousedown.select2."+n.id,(function(n){var s=e(n.target).closest(".select2");e(".select2.select2-container--open").each((function(){this!=s[0]&&t.GetData(this,"element").select2("close")}))}))},s.prototype._detachCloseHandler=function(t){e(document.body).off("mousedown.select2."+t.id)},s.prototype.position=function(e,t){t.find(".selection").append(e)},s.prototype.destroy=function(){this._detachCloseHandler(this.container)},s.prototype.update=function(e){throw new Error("The `update` method must be defined in child classes.")},s.prototype.isEnabled=function(){return!this.isDisabled()},s.prototype.isDisabled=function(){return this.options.get("disabled")},s})),x.define("select2/selection/single",["jquery","./base","../utils","../keys"],(function(e,t,n,s){function i(){i.__super__.constructor.apply(this,arguments)}return n.Extend(i,t),i.prototype.render=function(){var e=i.__super__.render.call(this);return e[0].classList.add("select2-selection--single"),e.html('<span class="select2-selection__rendered"></span><span class="select2-selection__arrow" role="presentation"><b role="presentation"></b></span>'),e},i.prototype.bind=function(e,t){var n=this;i.__super__.bind.apply(this,arguments);var s=e.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",s).attr("role","textbox").attr("aria-readonly","true"),this.$selection.attr("aria-labelledby",s),this.$selection.attr("aria-controls",s),this.$selection.on("mousedown",(function(e){1===e.which&&n.trigger("toggle",{originalEvent:e})})),this.$selection.on("focus",(function(e){})),this.$selection.on("blur",(function(e){})),e.on("focus",(function(t){e.isOpen()||n.$selection.trigger("focus")}))},i.prototype.clear=function(){var e=this.$selection.find(".select2-selection__rendered");e.empty(),e.removeAttr("title")},i.prototype.display=function(e,t){var n=this.options.get("templateSelection");return this.options.get("escapeMarkup")(n(e,t))},i.prototype.selectionContainer=function(){return e("<span></span>")},i.prototype.update=function(e){var t,n;0!==e.length?(n=e[0],t=this.$selection.find(".select2-selection__rendered"),e=this.display(n,t),t.empty().append(e),(n=n.title||n.text)?t.attr("title",n):t.removeAttr("title")):this.clear()},i})),x.define("select2/selection/multiple",["jquery","./base","../utils"],(function(e,t,n){function s(e,t){s.__super__.constructor.apply(this,arguments)}return n.Extend(s,t),s.prototype.render=function(){var e=s.__super__.render.call(this);return e[0].classList.add("select2-selection--multiple"),e.html('<ul class="select2-selection__rendered"></ul>'),e},s.prototype.bind=function(t,i){var r=this;s.__super__.bind.apply(this,arguments);var o=t.id+"-container";this.$selection.find(".select2-selection__rendered").attr("id",o),this.$selection.on("click",(function(e){r.trigger("toggle",{originalEvent:e})})),this.$selection.on("click",".select2-selection__choice__remove",(function(t){var s;r.isDisabled()||(s=e(this).parent(),s=n.GetData(s[0],"data"),r.trigger("unselect",{originalEvent:t,data:s}))})),this.$selection.on("keydown",".select2-selection__choice__remove",(function(e){r.isDisabled()||e.stopPropagation()}))},s.prototype.clear=function(){var e=this.$selection.find(".select2-selection__rendered");e.empty(),e.removeAttr("title")},s.prototype.display=function(e,t){var n=this.options.get("templateSelection");return this.options.get("escapeMarkup")(n(e,t))},s.prototype.selectionContainer=function(){return e('<li class="select2-selection__choice"><button type="button" class="select2-selection__choice__remove" tabindex="-1"><span aria-hidden="true">&times;</span></button><span class="select2-selection__choice__display"></span></li>')},s.prototype.update=function(e){if(this.clear(),0!==e.length){for(var t=[],s=this.$selection.find(".select2-selection__rendered").attr("id")+"-choice-",i=0;i<e.length;i++){var r=e[i],o=this.selectionContainer(),a=this.display(r,o),l=s+n.generateChars(4)+"-";r.id?l+=r.id:l+=n.generateChars(4),o.find(".select2-selection__choice__display").append(a).attr("id",l);var c=r.title||r.text;c&&o.attr("title",c),a=this.options.get("translations").get("removeItem"),(c=o.find(".select2-selection__choice__remove")).attr("title",a()),c.attr("aria-label",a()),c.attr("aria-describedby",l),n.StoreData(o[0],"data",r),t.push(o)}this.$selection.find(".select2-selection__rendered").append(t)}},s})),x.define("select2/selection/placeholder",[],(function(){function e(e,t,n){this.placeholder=this.normalizePlaceholder(n.get("placeholder")),e.call(this,t,n)}return e.prototype.normalizePlaceholder=function(e,t){return"string"==typeof t?{id:"",text:t}:t},e.prototype.createPlaceholder=function(e,t){var n=this.selectionContainer();return n.html(this.display(t)),n[0].classList.add("select2-selection__placeholder"),n[0].classList.remove("select2-selection__choice"),t=t.title||t.text||n.text(),this.$selection.find(".select2-selection__rendered").attr("title",t),n},e.prototype.update=function(e,t){var n=1==t.length&&t[0].id!=this.placeholder.id;if(1<t.length||n)return e.call(this,t);this.clear(),t=this.createPlaceholder(this.placeholder),this.$selection.find(".select2-selection__rendered").append(t)},e})),x.define("select2/selection/allowClear",["jquery","../keys","../utils"],(function(e,t,n){function s(){}return s.prototype.bind=function(e,t,n){var s=this;e.call(this,t,n),null==this.placeholder&&this.options.get("debug")&&window.console&&console.error&&console.error("Select2: The `allowClear` option should be used in combination with the `placeholder` option."),this.$selection.on("mousedown",".select2-selection__clear",(function(e){s._handleClear(e)})),t.on("keypress",(function(e){s._handleKeyboardClear(e,t)}))},s.prototype._handleClear=function(e,t){if(!this.isDisabled()){var s=this.$selection.find(".select2-selection__clear");if(0!==s.length){t.stopPropagation();var i=n.GetData(s[0],"data"),r=this.$element.val();this.$element.val(this.placeholder.id);var o={data:i};if(this.trigger("clear",o),o.prevented)this.$element.val(r);else{for(var a=0;a<i.length;a++)if(o={data:i[a]},this.trigger("unselect",o),o.prevented)return void this.$element.val(r);this.$element.trigger("input").trigger("change"),this.trigger("toggle",{})}}}},s.prototype._handleKeyboardClear=function(e,n,s){s.isOpen()||n.which!=t.DELETE&&n.which!=t.BACKSPACE||this._handleClear(n)},s.prototype.update=function(t,s){var i,r;t.call(this,s),this.$selection.find(".select2-selection__clear").remove(),this.$selection[0].classList.remove("select2-selection--clearable"),0<this.$selection.find(".select2-selection__placeholder").length||0===s.length||(i=this.$selection.find(".select2-selection__rendered").attr("id"),r=this.options.get("translations").get("removeAllItems"),(t=e('<button type="button" class="select2-selection__clear" tabindex="-1"><span aria-hidden="true">&times;</span></button>')).attr("title",r()),t.attr("aria-label",r()),t.attr("aria-describedby",i),n.StoreData(t[0],"data",s),this.$selection.prepend(t),this.$selection[0].classList.add("select2-selection--clearable"))},s})),x.define("select2/selection/search",["jquery","../utils","../keys"],(function(e,t,n){function s(e,t,n){e.call(this,t,n)}return s.prototype.render=function(t){var n=this.options.get("translations").get("search"),s=e('<span class="select2-search select2-search--inline"><textarea class="select2-search__field" type="search" tabindex="-1" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" ></textarea></span>');return this.$searchContainer=s,this.$search=s.find("textarea"),this.$search.prop("autocomplete",this.options.get("autocomplete")),this.$search.attr("aria-label",n()),t=t.call(this),this._transferTabIndex(),t.append(this.$searchContainer),t},s.prototype.bind=function(e,s,i){var r=this,o=s.id+"-results",a=s.id+"-container";e.call(this,s,i),r.$search.attr("aria-describedby",a),s.on("open",(function(){r.$search.attr("aria-controls",o),r.$search.trigger("focus")})),s.on("close",(function(){r.$search.val(""),r.resizeSearch(),r.$search.removeAttr("aria-controls"),r.$search.removeAttr("aria-activedescendant"),r.$search.trigger("focus")})),s.on("enable",(function(){r.$search.prop("disabled",!1),r._transferTabIndex()})),s.on("disable",(function(){r.$search.prop("disabled",!0)})),s.on("focus",(function(e){r.$search.trigger("focus")})),s.on("results:focus",(function(e){e.data._resultId?r.$search.attr("aria-activedescendant",e.data._resultId):r.$search.removeAttr("aria-activedescendant")})),this.$selection.on("focusin",".select2-search--inline",(function(e){r.trigger("focus",e)})),this.$selection.on("focusout",".select2-search--inline",(function(e){r._handleBlur(e)})),this.$selection.on("keydown",".select2-search--inline",(function(e){var s;e.stopPropagation(),r.trigger("keypress",e),r._keyUpPrevented=e.isDefaultPrevented(),e.which!==n.BACKSPACE||""!==r.$search.val()||0<(s=r.$selection.find(".select2-selection__choice").last()).length&&(s=t.GetData(s[0],"data"),r.searchRemoveChoice(s),e.preventDefault())})),this.$selection.on("click",".select2-search--inline",(function(e){r.$search.val()&&e.stopPropagation()}));var l=(s=document.documentMode)&&s<=11;this.$selection.on("input.searchcheck",".select2-search--inline",(function(e){l?r.$selection.off("input.search input.searchcheck"):r.$selection.off("keyup.search")})),this.$selection.on("keyup.search input.search",".select2-search--inline",(function(e){var t;l&&"input"===e.type?r.$selection.off("input.search input.searchcheck"):(t=e.which)!=n.SHIFT&&t!=n.CTRL&&t!=n.ALT&&t!=n.TAB&&r.handleSearch(e)}))},s.prototype._transferTabIndex=function(e){this.$search.attr("tabindex",this.$selection.attr("tabindex")),this.$selection.attr("tabindex","-1")},s.prototype.createPlaceholder=function(e,t){this.$search.attr("placeholder",t.text)},s.prototype.update=function(e,t){var n=this.$search[0]==document.activeElement;this.$search.attr("placeholder",""),e.call(this,t),this.resizeSearch(),n&&this.$search.trigger("focus")},s.prototype.handleSearch=function(){var e;this.resizeSearch(),this._keyUpPrevented||(e=this.$search.val(),this.trigger("query",{term:e})),this._keyUpPrevented=!1},s.prototype.searchRemoveChoice=function(e,t){this.trigger("unselect",{data:t}),this.$search.val(t.text),this.handleSearch()},s.prototype.resizeSearch=function(){this.$search.css("width","25px");var e="100%";""===this.$search.attr("placeholder")&&(e=.75*(this.$search.val().length+1)+"em"),this.$search.css("width",e)},s})),x.define("select2/selection/selectionCss",["../utils"],(function(e){function t(){}return t.prototype.render=function(t){var n=t.call(this);return-1!==(t=this.options.get("selectionCssClass")||"").indexOf(":all:")&&(t=t.replace(":all:",""),e.copyNonInternalCssClasses(n[0],this.$element[0])),n.addClass(t),n},t})),x.define("select2/selection/eventRelay",["jquery"],(function(e){function t(){}return t.prototype.bind=function(t,n,s){var i=this,r=["open","opening","close","closing","select","selecting","unselect","unselecting","clear","clearing"],o=["opening","closing","selecting","unselecting","clearing"];t.call(this,n,s),n.on("*",(function(t,n){var s;-1!==r.indexOf(t)&&(n=n||{},s=e.Event("select2:"+t,{params:n}),i.$element.trigger(s),-1!==o.indexOf(t)&&(n.prevented=s.isDefaultPrevented()))}))},t})),x.define("select2/translation",["jquery","require"],(function(e,t){function n(e){this.dict=e||{}}return n.prototype.all=function(){return this.dict},n.prototype.get=function(e){return this.dict[e]},n.prototype.extend=function(t){this.dict=e.extend({},t.all(),this.dict)},n._cache={},n.loadPath=function(e){var s;return e in n._cache||(s=t(e),n._cache[e]=s),new n(n._cache[e])},n})),x.define("select2/diacritics",[],(function(){return{"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Œ":"OE","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","œ":"oe","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ώ":"ω","ς":"σ","’":"'"}})),x.define("select2/data/base",["../utils"],(function(e){function t(e,n){t.__super__.constructor.call(this)}return e.Extend(t,e.Observable),t.prototype.current=function(e){throw new Error("The `current` method must be defined in child classes.")},t.prototype.query=function(e,t){throw new Error("The `query` method must be defined in child classes.")},t.prototype.bind=function(e,t){},t.prototype.destroy=function(){},t.prototype.generateResultId=function(t,n){return t=t.id+"-result-",t+=e.generateChars(4),null!=n.id?t+="-"+n.id.toString():t+="-"+e.generateChars(4),t},t})),x.define("select2/data/select",["./base","../utils","jquery"],(function(e,t,n){function s(e,t){this.$element=e,this.options=t,s.__super__.constructor.call(this)}return t.Extend(s,e),s.prototype.current=function(e){var t=this;e(Array.prototype.map.call(this.$element[0].querySelectorAll(":checked"),(function(e){return t.item(n(e))})))},s.prototype.select=function(e){var t,n=this;if(e.selected=!0,null!=e.element&&"option"===e.element.tagName.toLowerCase())return e.element.selected=!0,void this.$element.trigger("input").trigger("change");this.$element.prop("multiple")?this.current((function(t){var s=[];(e=[e]).push.apply(e,t);for(var i=0;i<e.length;i++){var r=e[i].id;-1===s.indexOf(r)&&s.push(r)}n.$element.val(s),n.$element.trigger("input").trigger("change")})):(t=e.id,this.$element.val(t),this.$element.trigger("input").trigger("change"))},s.prototype.unselect=function(e){var t=this;if(this.$element.prop("multiple")){if(e.selected=!1,null!=e.element&&"option"===e.element.tagName.toLowerCase())return e.element.selected=!1,void this.$element.trigger("input").trigger("change");this.current((function(n){for(var s=[],i=0;i<n.length;i++){var r=n[i].id;r!==e.id&&-1===s.indexOf(r)&&s.push(r)}t.$element.val(s),t.$element.trigger("input").trigger("change")}))}},s.prototype.bind=function(e,t){var n=this;(this.container=e).on("select",(function(e){n.select(e.data)})),e.on("unselect",(function(e){n.unselect(e.data)}))},s.prototype.destroy=function(){this.$element.find("*").each((function(){t.RemoveData(this)}))},s.prototype.query=function(e,t){var s=[],i=this;this.$element.children().each((function(){var t;"option"!==this.tagName.toLowerCase()&&"optgroup"!==this.tagName.toLowerCase()||(t=n(this),t=i.item(t),null!==(t=i.matches(e,t))&&s.push(t))})),t({results:s})},s.prototype.addOptions=function(e){this.$element.append(e)},s.prototype.option=function(e){var s;return e.children?(s=document.createElement("optgroup")).label=e.text:void 0!==(s=document.createElement("option")).textContent?s.textContent=e.text:s.innerText=e.text,void 0!==e.id&&(s.value=e.id),e.disabled&&(s.disabled=!0),e.selected&&(s.selected=!0),e.title&&(s.title=e.title),(e=this._normalizeItem(e)).element=s,t.StoreData(s,"data",e),n(s)},s.prototype.item=function(e){var s={};if(null!=(s=t.GetData(e[0],"data")))return s;var i=e[0];if("option"===i.tagName.toLowerCase())s={id:e.val(),text:e.text(),disabled:e.prop("disabled"),selected:e.prop("selected"),title:e.prop("title")};else if("optgroup"===i.tagName.toLowerCase()){s={text:e.prop("label"),children:[],title:e.prop("title")};for(var r=e.children("option"),o=[],a=0;a<r.length;a++){var l=n(r[a]);l=this.item(l);o.push(l)}s.children=o}return(s=this._normalizeItem(s)).element=e[0],t.StoreData(e[0],"data",s),s},s.prototype._normalizeItem=function(e){return e!==Object(e)&&(e={id:e,text:e}),null!=(e=n.extend({},{text:""},e)).id&&(e.id=e.id.toString()),null!=e.text&&(e.text=e.text.toString()),null==e._resultId&&e.id&&null!=this.container&&(e._resultId=this.generateResultId(this.container,e)),n.extend({},{selected:!1,disabled:!1},e)},s.prototype.matches=function(e,t){return this.options.get("matcher")(e,t)},s})),x.define("select2/data/array",["./select","../utils","jquery"],(function(e,t,n){function s(e,t){this._dataToConvert=t.get("data")||[],s.__super__.constructor.call(this,e,t)}return t.Extend(s,e),s.prototype.bind=function(e,t){s.__super__.bind.call(this,e,t),this.addOptions(this.convertToOptions(this._dataToConvert))},s.prototype.select=function(e){var t=this.$element.find("option").filter((function(t,n){return n.value==e.id.toString()}));0===t.length&&(t=this.option(e),this.addOptions(t)),s.__super__.select.call(this,e)},s.prototype.convertToOptions=function(e){for(var t=this,s=this.$element.find("option"),i=s.map((function(){return t.item(n(this)).id})).get(),r=[],o=0;o<e.length;o++){var a,l,c=this._normalizeItem(e[o]);0<=i.indexOf(c.id)?(a=s.filter(function(e){return function(){return n(this).val()==e.id}}(c)),l=this.item(a),l=n.extend(!0,{},c,l),l=this.option(l),a.replaceWith(l)):(l=this.option(c),c.children&&(c=this.convertToOptions(c.children),l.append(c)),r.push(l))}return r},s})),x.define("select2/data/ajax",["./array","../utils","jquery"],(function(e,t,n){function s(e,t){this.ajaxOptions=this._applyDefaults(t.get("ajax")),null!=this.ajaxOptions.processResults&&(this.processResults=this.ajaxOptions.processResults),s.__super__.constructor.call(this,e,t)}return t.Extend(s,e),s.prototype._applyDefaults=function(e){var t={data:function(e){return n.extend({},e,{q:e.term})},transport:function(e,t,s){return(e=n.ajax(e)).then(t),e.fail(s),e}};return n.extend({},t,e,!0)},s.prototype.processResults=function(e){return e},s.prototype.query=function(e,t){var s=this;null!=this._request&&("function"==typeof this._request.abort&&this._request.abort(),this._request=null);var i=n.extend({type:"GET"},this.ajaxOptions);function r(){var n=i.transport(i,(function(n){n=s.processResults(n,e),s.options.get("debug")&&window.console&&console.error&&(n&&n.results&&Array.isArray(n.results)||console.error("Select2: The AJAX results did not return an array in the `results` key of the response.")),t(n)}),(function(){"status"in n&&(0===n.status||"0"===n.status)||s.trigger("results:message",{message:"errorLoading"})}));s._request=n}"function"==typeof i.url&&(i.url=i.url.call(this.$element,e)),"function"==typeof i.data&&(i.data=i.data.call(this.$element,e)),this.ajaxOptions.delay&&null!=e.term?(this._queryTimeout&&window.clearTimeout(this._queryTimeout),this._queryTimeout=window.setTimeout(r,this.ajaxOptions.delay)):r()},s})),x.define("select2/data/tags",["jquery"],(function(e){function t(e,t,n){var s=n.get("tags"),i=n.get("createTag");if(void 0!==i&&(this.createTag=i),void 0!==(i=n.get("insertTag"))&&(this.insertTag=i),e.call(this,t,n),Array.isArray(s))for(var r=0;r<s.length;r++){var o=s[r];o=this._normalizeItem(o),o=this.option(o);this.$element.append(o)}}return t.prototype.query=function(e,t,n){var s=this;this._removeOldTags(),null!=t.term&&null==t.page?e.call(this,t,(function i(e,r){for(var o=e.results,a=0;a<o.length;a++){var l=o[a],c=null!=l.children&&!i({results:l.children},!0);if((l.text||"").toUpperCase()===(t.term||"").toUpperCase()||c)return!r&&(e.data=o,void n(e))}if(r)return!0;var u,d=s.createTag(t);null!=d&&((u=s.option(d)).attr("data-select2-tag","true"),s.addOptions([u]),s.insertTag(o,d)),e.results=o,n(e)})):e.call(this,t,n)},t.prototype.createTag=function(e,t){return null==t.term||""===(t=t.term.trim())?null:{id:t,text:t}},t.prototype.insertTag=function(e,t,n){t.unshift(n)},t.prototype._removeOldTags=function(t){this.$element.find("option[data-select2-tag]").each((function(){this.selected||e(this).remove()}))},t})),x.define("select2/data/tokenizer",["jquery"],(function(e){function t(e,t,n){var s=n.get("tokenizer");void 0!==s&&(this.tokenizer=s),e.call(this,t,n)}return t.prototype.bind=function(e,t,n){e.call(this,t,n),this.$search=t.dropdown.$search||t.selection.$search||n.find(".select2-search__field")},t.prototype.query=function(t,n,s){var i=this;n.term=n.term||"";var r=this.tokenizer(n,this.options,(function(t){var n,s=i._normalizeItem(t);i.$element.find("option").filter((function(){return e(this).val()===s.id})).length||((n=i.option(s)).attr("data-select2-tag",!0),i._removeOldTags(),i.addOptions([n])),n=s,i.trigger("select",{data:n})}));r.term!==n.term&&(this.$search.length&&(this.$search.val(r.term),this.$search.trigger("focus")),n.term=r.term),t.call(this,n,s)},t.prototype.tokenizer=function(t,n,s,i){for(var r=s.get("tokenSeparators")||[],o=n.term,a=0,l=this.createTag||function(e){return{id:e.term,text:e.term}};a<o.length;){var c=o[a];-1!==r.indexOf(c)?(c=o.substr(0,a),null!=(c=l(e.extend({},n,{term:c})))?(i(c),o=o.substr(a+1)||"",a=0):a++):a++}return{term:o}},t})),x.define("select2/data/minimumInputLength",[],(function(){function e(e,t,n){this.minimumInputLength=n.get("minimumInputLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){t.term=t.term||"",t.term.length<this.minimumInputLength?this.trigger("results:message",{message:"inputTooShort",args:{minimum:this.minimumInputLength,input:t.term,params:t}}):e.call(this,t,n)},e})),x.define("select2/data/maximumInputLength",[],(function(){function e(e,t,n){this.maximumInputLength=n.get("maximumInputLength"),e.call(this,t,n)}return e.prototype.query=function(e,t,n){t.term=t.term||"",0<this.maximumInputLength&&t.term.length>this.maximumInputLength?this.trigger("results:message",{message:"inputTooLong",args:{maximum:this.maximumInputLength,input:t.term,params:t}}):e.call(this,t,n)},e})),x.define("select2/data/maximumSelectionLength",[],(function(){function e(e,t,n){this.maximumSelectionLength=n.get("maximumSelectionLength"),e.call(this,t,n)}return e.prototype.bind=function(e,t,n){var s=this;e.call(this,t,n),t.on("select",(function(){s._checkIfMaximumSelected()}))},e.prototype.query=function(e,t,n){var s=this;this._checkIfMaximumSelected((function(){e.call(s,t,n)}))},e.prototype._checkIfMaximumSelected=function(e,t){var n=this;this.current((function(e){e=null!=e?e.length:0,0<n.maximumSelectionLength&&e>=n.maximumSelectionLength?n.trigger("results:message",{message:"maximumSelected",args:{maximum:n.maximumSelectionLength}}):t&&t()}))},e})),x.define("select2/dropdown",["jquery","./utils"],(function(e,t){function n(e,t){this.$element=e,this.options=t,n.__super__.constructor.call(this)}return t.Extend(n,t.Observable),n.prototype.render=function(){var t=e('<span class="select2-dropdown"><span class="select2-results"></span></span>');return t.attr("dir",this.options.get("dir")),this.$dropdown=t},n.prototype.bind=function(){},n.prototype.position=function(e,t){},n.prototype.destroy=function(){this.$dropdown.remove()},n})),x.define("select2/dropdown/search",["jquery"],(function(e){function t(){}return t.prototype.render=function(t){var n=t.call(this),s=this.options.get("translations").get("search");t=e('<span class="select2-search select2-search--dropdown"><input class="select2-search__field" type="search" tabindex="-1" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" /></span>');return this.$searchContainer=t,this.$search=t.find("input"),this.$search.prop("autocomplete",this.options.get("autocomplete")),this.$search.attr("aria-label",s()),n.prepend(t),n},t.prototype.bind=function(t,n,s){var i=this,r=n.id+"-results";t.call(this,n,s),this.$search.on("keydown",(function(e){i.trigger("keypress",e),i._keyUpPrevented=e.isDefaultPrevented()})),this.$search.on("input",(function(t){e(this).off("keyup")})),this.$search.on("keyup input",(function(e){i.handleSearch(e)})),n.on("open",(function(){i.$search.attr("tabindex",0),i.$search.attr("aria-controls",r),i.$search.trigger("focus"),window.setTimeout((function(){i.$search.trigger("focus")}),0)})),n.on("close",(function(){i.$search.attr("tabindex",-1),i.$search.removeAttr("aria-controls"),i.$search.removeAttr("aria-activedescendant"),i.$search.val(""),i.$search.trigger("blur")})),n.on("focus",(function(){n.isOpen()||i.$search.trigger("focus")})),n.on("results:all",(function(e){null!=e.query.term&&""!==e.query.term||(i.showSearch(e)?i.$searchContainer[0].classList.remove("select2-search--hide"):i.$searchContainer[0].classList.add("select2-search--hide"))})),n.on("results:focus",(function(e){e.data._resultId?i.$search.attr("aria-activedescendant",e.data._resultId):i.$search.removeAttr("aria-activedescendant")}))},t.prototype.handleSearch=function(e){var t;this._keyUpPrevented||(t=this.$search.val(),this.trigger("query",{term:t})),this._keyUpPrevented=!1},t.prototype.showSearch=function(e,t){return!0},t})),x.define("select2/dropdown/hidePlaceholder",[],(function(){function e(e,t,n,s){this.placeholder=this.normalizePlaceholder(n.get("placeholder")),e.call(this,t,n,s)}return e.prototype.append=function(e,t){t.results=this.removePlaceholder(t.results),e.call(this,t)},e.prototype.normalizePlaceholder=function(e,t){return"string"==typeof t?{id:"",text:t}:t},e.prototype.removePlaceholder=function(e,t){for(var n=t.slice(0),s=t.length-1;0<=s;s--){var i=t[s];this.placeholder.id===i.id&&n.splice(s,1)}return n},e})),x.define("select2/dropdown/infiniteScroll",["jquery"],(function(e){function t(e,t,n,s){this.lastParams={},e.call(this,t,n,s),this.$loadingMore=this.createLoadingMore(),this.loading=!1}return t.prototype.append=function(e,t){this.$loadingMore.remove(),this.loading=!1,e.call(this,t),this.showLoadingMore(t)&&(this.$results.append(this.$loadingMore),this.loadMoreIfNeeded())},t.prototype.bind=function(e,t,n){var s=this;e.call(this,t,n),t.on("query",(function(e){s.lastParams=e,s.loading=!0})),t.on("query:append",(function(e){s.lastParams=e,s.loading=!0})),this.$results.on("scroll",this.loadMoreIfNeeded.bind(this))},t.prototype.loadMoreIfNeeded=function(){var t=e.contains(document.documentElement,this.$loadingMore[0]);!this.loading&&t&&(t=this.$results.offset().top+this.$results.outerHeight(!1),this.$loadingMore.offset().top+this.$loadingMore.outerHeight(!1)<=t+50&&this.loadMore())},t.prototype.loadMore=function(){this.loading=!0;var t=e.extend({},{page:1},this.lastParams);t.page++,this.trigger("query:append",t)},t.prototype.showLoadingMore=function(e,t){return t.pagination&&t.pagination.more},t.prototype.createLoadingMore=function(){var t=e('<li class="select2-results__option select2-results__option--load-more"role="option" aria-disabled="true"></li>'),n=this.options.get("translations").get("loadingMore");return t.html(n(this.lastParams)),t},t})),x.define("select2/dropdown/attachBody",["jquery","../utils"],(function(e,t){function n(t,n,s){this.$dropdownParent=e(s.get("dropdownParent")||document.body),t.call(this,n,s)}return n.prototype.bind=function(e,t,n){var s=this;e.call(this,t,n),t.on("open",(function(){s._showDropdown(),s._attachPositioningHandler(t),s._bindContainerResultHandlers(t)})),t.on("close",(function(){s._hideDropdown(),s._detachPositioningHandler(t)})),this.$dropdownContainer.on("mousedown",(function(e){e.stopPropagation()}))},n.prototype.destroy=function(e){e.call(this),this.$dropdownContainer.remove()},n.prototype.position=function(e,t,n){t.attr("class",n.attr("class")),t[0].classList.remove("select2"),t[0].classList.add("select2-container--open"),t.css({position:"absolute",top:-999999}),this.$container=n},n.prototype.render=function(t){var n=e("<span></span>");t=t.call(this);return n.append(t),this.$dropdownContainer=n},n.prototype._hideDropdown=function(e){this.$dropdownContainer.detach()},n.prototype._bindContainerResultHandlers=function(e,t){var n;this._containerResultsHandlersBound||(n=this,t.on("results:all",(function(){n._positionDropdown(),n._resizeDropdown()})),t.on("results:append",(function(){n._positionDropdown(),n._resizeDropdown()})),t.on("results:message",(function(){n._positionDropdown(),n._resizeDropdown()})),t.on("select",(function(){n._positionDropdown(),n._resizeDropdown()})),t.on("unselect",(function(){n._positionDropdown(),n._resizeDropdown()})),this._containerResultsHandlersBound=!0)},n.prototype._attachPositioningHandler=function(n,s){var i=this,r="scroll.select2."+s.id,o="resize.select2."+s.id,a="orientationchange.select2."+s.id;(s=this.$container.parents().filter(t.hasScroll)).each((function(){t.StoreData(this,"select2-scroll-position",{x:e(this).scrollLeft(),y:e(this).scrollTop()})})),s.on(r,(function(n){var s=t.GetData(this,"select2-scroll-position");e(this).scrollTop(s.y)})),e(window).on(r+" "+o+" "+a,(function(e){i._positionDropdown(),i._resizeDropdown()}))},n.prototype._detachPositioningHandler=function(n,s){var i="scroll.select2."+s.id,r="resize.select2."+s.id;s="orientationchange.select2."+s.id;this.$container.parents().filter(t.hasScroll).off(i),e(window).off(i+" "+r+" "+s)},n.prototype._positionDropdown=function(){var t=e(window),n=this.$dropdown[0].classList.contains("select2-dropdown--above"),s=this.$dropdown[0].classList.contains("select2-dropdown--below"),i=null,r=this.$container.offset();r.bottom=r.top+this.$container.outerHeight(!1);var o={height:this.$container.outerHeight(!1)};o.top=r.top,o.bottom=r.top+o.height;var a=this.$dropdown.outerHeight(!1),l=t.scrollTop(),c=t.scrollTop()+t.height(),u=l<r.top-a;t=c>r.bottom+a,l={left:r.left,top:o.bottom};"static"===(c=this.$dropdownParent).css("position")&&(c=c.offsetParent()),r={top:0,left:0},(e.contains(document.body,c[0])||c[0].isConnected)&&(r=c.offset()),l.top-=r.top,l.left-=r.left,n||s||(i="below"),t||!u||n?!u&&t&&n&&(i="below"):i="above",("above"==i||n&&"below"!==i)&&(l.top=o.top-r.top-a),null!=i&&(this.$dropdown[0].classList.remove("select2-dropdown--below"),this.$dropdown[0].classList.remove("select2-dropdown--above"),this.$dropdown[0].classList.add("select2-dropdown--"+i),this.$container[0].classList.remove("select2-container--below"),this.$container[0].classList.remove("select2-container--above"),this.$container[0].classList.add("select2-container--"+i)),this.$dropdownContainer.css(l)},n.prototype._resizeDropdown=function(){var e={width:this.$container.outerWidth(!1)+"px"};this.options.get("dropdownAutoWidth")&&(e.minWidth=e.width,e.position="relative",e.width="auto"),this.$dropdown.css(e)},n.prototype._showDropdown=function(e){this.$dropdownContainer.appendTo(this.$dropdownParent),this._positionDropdown(),this._resizeDropdown()},n})),x.define("select2/dropdown/minimumResultsForSearch",[],(function(){function e(e,t,n,s){this.minimumResultsForSearch=n.get("minimumResultsForSearch"),this.minimumResultsForSearch<0&&(this.minimumResultsForSearch=1/0),e.call(this,t,n,s)}return e.prototype.showSearch=function(e,t){return!(function n(e){for(var t=0,s=0;s<e.length;s++){var i=e[s];i.children?t+=n(i.children):t++}return t}(t.data.results)<this.minimumResultsForSearch)&&e.call(this,t)},e})),x.define("select2/dropdown/selectOnClose",["../utils"],(function(e){function t(){}return t.prototype.bind=function(e,t,n){var s=this;e.call(this,t,n),t.on("close",(function(e){s._handleSelectOnClose(e)}))},t.prototype._handleSelectOnClose=function(t,n){if(n&&null!=n.originalSelect2Event){var s=n.originalSelect2Event;if("select"===s._type||"unselect"===s._type)return}(s=this.getHighlightedResults()).length<1||null!=(s=e.GetData(s[0],"data")).element&&s.element.selected||null==s.element&&s.selected||this.trigger("select",{data:s})},t})),x.define("select2/dropdown/closeOnSelect",[],(function(){function e(){}return e.prototype.bind=function(e,t,n){var s=this;e.call(this,t,n),t.on("select",(function(e){s._selectTriggered(e)})),t.on("unselect",(function(e){s._selectTriggered(e)}))},e.prototype._selectTriggered=function(e,t){var n=t.originalEvent;n&&(n.ctrlKey||n.metaKey)||this.trigger("close",{originalEvent:n,originalSelect2Event:t})},e})),x.define("select2/dropdown/dropdownCss",["../utils"],(function(e){function t(){}return t.prototype.render=function(t){var n=t.call(this);return-1!==(t=this.options.get("dropdownCssClass")||"").indexOf(":all:")&&(t=t.replace(":all:",""),e.copyNonInternalCssClasses(n[0],this.$element[0])),n.addClass(t),n},t})),x.define("select2/dropdown/tagsSearchHighlight",["../utils"],(function(e){function t(){}return t.prototype.highlightFirstItem=function(t){if(0<(n=this.$results.find(".select2-results__option--selectable:not(.select2-results__option--selected)")).length){var n,s=n.first();if((n=e.GetData(s[0],"data").element)&&n.getAttribute&&"true"===n.getAttribute("data-select2-tag"))return void s.trigger("mouseenter")}t.call(this)},t})),x.define("select2/i18n/en",[],(function(){return{errorLoading:function(){return"The results could not be loaded."},inputTooLong:function(e){var t=e.input.length-e.maximum;e="Please delete "+t+" character";return 1!=t&&(e+="s"),e},inputTooShort:function(e){return"Please enter "+(e.minimum-e.input.length)+" or more characters"},loadingMore:function(){return"Loading more results…"},maximumSelected:function(e){var t="You can only select "+e.maximum+" item";return 1!=e.maximum&&(t+="s"),t},noResults:function(){return"No results found"},searching:function(){return"Searching…"},removeAllItems:function(){return"Remove all items"},removeItem:function(){return"Remove item"},search:function(){return"Search"}}})),x.define("select2/defaults",["jquery","./results","./selection/single","./selection/multiple","./selection/placeholder","./selection/allowClear","./selection/search","./selection/selectionCss","./selection/eventRelay","./utils","./translation","./diacritics","./data/select","./data/array","./data/ajax","./data/tags","./data/tokenizer","./data/minimumInputLength","./data/maximumInputLength","./data/maximumSelectionLength","./dropdown","./dropdown/search","./dropdown/hidePlaceholder","./dropdown/infiniteScroll","./dropdown/attachBody","./dropdown/minimumResultsForSearch","./dropdown/selectOnClose","./dropdown/closeOnSelect","./dropdown/dropdownCss","./dropdown/tagsSearchHighlight","./i18n/en"],(function(e,t,n,s,i,r,o,a,l,c,u,d,p,h,f,g,m,y,v,_,b,$,w,x,A,D,S,E,O,C,L){function T(){this.reset()}return T.prototype.apply=function(u){var d;null==(u=e.extend(!0,{},this.defaults,u)).dataAdapter&&(null!=u.ajax?u.dataAdapter=f:null!=u.data?u.dataAdapter=h:u.dataAdapter=p,0<u.minimumInputLength&&(u.dataAdapter=c.Decorate(u.dataAdapter,y)),0<u.maximumInputLength&&(u.dataAdapter=c.Decorate(u.dataAdapter,v)),0<u.maximumSelectionLength&&(u.dataAdapter=c.Decorate(u.dataAdapter,_)),u.tags&&(u.dataAdapter=c.Decorate(u.dataAdapter,g)),null==u.tokenSeparators&&null==u.tokenizer||(u.dataAdapter=c.Decorate(u.dataAdapter,m))),null==u.resultsAdapter&&(u.resultsAdapter=t,null!=u.ajax&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,x)),null!=u.placeholder&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,w)),u.selectOnClose&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,S)),u.tags&&(u.resultsAdapter=c.Decorate(u.resultsAdapter,C))),null==u.dropdownAdapter&&(u.multiple?u.dropdownAdapter=b:(d=c.Decorate(b,$),u.dropdownAdapter=d),0!==u.minimumResultsForSearch&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,D)),u.closeOnSelect&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,E)),null!=u.dropdownCssClass&&(u.dropdownAdapter=c.Decorate(u.dropdownAdapter,O)),u.dropdownAdapter=c.Decorate(u.dropdownAdapter,A)),null==u.selectionAdapter&&(u.multiple?u.selectionAdapter=s:u.selectionAdapter=n,null!=u.placeholder&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,i)),u.allowClear&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,r)),u.multiple&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,o)),null!=u.selectionCssClass&&(u.selectionAdapter=c.Decorate(u.selectionAdapter,a)),u.selectionAdapter=c.Decorate(u.selectionAdapter,l)),u.language=this._resolveLanguage(u.language),u.language.push("en");for(var L=[],T=0;T<u.language.length;T++){var q=u.language[T];-1===L.indexOf(q)&&L.push(q)}return u.language=L,u.translations=this._processTranslations(u.language,u.debug),u},T.prototype.reset=function(){function t(e){return e.replace(/[^\u0000-\u007E]/g,(function(e){return d[e]||e}))}this.defaults={amdLanguageBase:"./i18n/",autocomplete:"off",closeOnSelect:!0,debug:!1,dropdownAutoWidth:!1,escapeMarkup:c.escapeMarkup,language:{},matcher:function n(s,i){if(null==s.term||""===s.term.trim())return i;if(i.children&&0<i.children.length){for(var r=e.extend(!0,{},i),o=i.children.length-1;0<=o;o--)null==n(s,i.children[o])&&r.children.splice(o,1);return 0<r.children.length?r:n(s,r)}var a=t(i.text).toUpperCase(),l=t(s.term).toUpperCase();return-1<a.indexOf(l)?i:null},minimumInputLength:0,maximumInputLength:0,maximumSelectionLength:0,minimumResultsForSearch:0,selectOnClose:!1,scrollAfterSelect:!1,sorter:function(e){return e},templateResult:function(e){return e.text},templateSelection:function(e){return e.text},theme:"default",width:"resolve"}},T.prototype.applyFromElement=function(e,t){var n=e.language,s=this.defaults.language,i=t.prop("lang");t=t.closest("[lang]").prop("lang"),t=Array.prototype.concat.call(this._resolveLanguage(i),this._resolveLanguage(n),this._resolveLanguage(s),this._resolveLanguage(t));return e.language=t,e},T.prototype._resolveLanguage=function(t){if(!t)return[];if(e.isEmptyObject(t))return[];if(e.isPlainObject(t))return[t];for(var n,s=Array.isArray(t)?t:[t],i=[],r=0;r<s.length;r++)i.push(s[r]),"string"==typeof s[r]&&0<s[r].indexOf("-")&&(n=s[r].split("-")[0],i.push(n));return i},T.prototype._processTranslations=function(t,n){for(var s=new u,i=0;i<t.length;i++){var r=new u,o=t[i];if("string"==typeof o)try{r=u.loadPath(o)}catch(t){try{o=this.defaults.amdLanguageBase+o,r=u.loadPath(o)}catch(t){n&&window.console&&console.warn&&console.warn('Select2: The language file for "'+o+'" could not be automatically loaded. A fallback will be used instead.')}}else r=e.isPlainObject(o)?new u(o):o;s.extend(r)}return s},T.prototype.set=function(t,n){var s={};s[e.camelCase(t)]=n,s=c._convertData(s),e.extend(!0,this.defaults,s)},new T})),x.define("select2/options",["jquery","./defaults","./utils"],(function(e,t,n){function s(e,n){this.options=e,null!=n&&this.fromElement(n),null!=n&&(this.options=t.applyFromElement(this.options,n)),this.options=t.apply(this.options)}return s.prototype.fromElement=function(t){var s=["select2"];null==this.options.multiple&&(this.options.multiple=t.prop("multiple")),null==this.options.disabled&&(this.options.disabled=t.prop("disabled")),null==this.options.autocomplete&&t.prop("autocomplete")&&(this.options.autocomplete=t.prop("autocomplete")),null==this.options.dir&&(t.prop("dir")?this.options.dir=t.prop("dir"):t.closest("[dir]").prop("dir")?this.options.dir=t.closest("[dir]").prop("dir"):this.options.dir="ltr"),t.prop("disabled",this.options.disabled),t.prop("multiple",this.options.multiple),n.GetData(t[0],"select2Tags")&&(this.options.debug&&window.console&&console.warn&&console.warn('Select2: The `data-select2-tags` attribute has been changed to use the `data-data` and `data-tags="true"` attributes and will be removed in future versions of Select2.'),n.StoreData(t[0],"data",n.GetData(t[0],"select2Tags")),n.StoreData(t[0],"tags",!0)),n.GetData(t[0],"ajaxUrl")&&(this.options.debug&&window.console&&console.warn&&console.warn("Select2: The `data-ajax-url` attribute has been changed to `data-ajax--url` and support for the old attribute will be removed in future versions of Select2."),t.attr("ajax--url",n.GetData(t[0],"ajaxUrl")),n.StoreData(t[0],"ajax-Url",n.GetData(t[0],"ajaxUrl")));var i={};function r(e,t){return t.toUpperCase()}for(var o=0;o<t[0].attributes.length;o++){var a=t[0].attributes[o].name,l="data-";a.substr(0,l.length)==l&&(a=a.substring(l.length),l=n.GetData(t[0],a),i[a.replace(/-([a-z])/g,r)]=l)}e.fn.jquery&&"1."==e.fn.jquery.substr(0,2)&&t[0].dataset&&(i=e.extend(!0,{},t[0].dataset,i));var c,u=e.extend(!0,{},n.GetData(t[0]),i);for(c in u=n._convertData(u))-1<s.indexOf(c)||(e.isPlainObject(this.options[c])?e.extend(this.options[c],u[c]):this.options[c]=u[c]);return this},s.prototype.get=function(e){return this.options[e]},s.prototype.set=function(e,t){this.options[e]=t},s})),x.define("select2/core",["jquery","./options","./utils","./keys"],(function(e,t,n,s){var i=function(e,s){null!=n.GetData(e[0],"select2")&&n.GetData(e[0],"select2").destroy(),this.$element=e,this.id=this._generateId(e),s=s||{},this.options=new t(s,e),i.__super__.constructor.call(this);var r=e.attr("tabindex")||0;n.StoreData(e[0],"old-tabindex",r),e.attr("tabindex","-1"),s=this.options.get("dataAdapter"),this.dataAdapter=new s(e,this.options),r=this.render(),this._placeContainer(r),s=this.options.get("selectionAdapter"),this.selection=new s(e,this.options),this.$selection=this.selection.render(),this.selection.position(this.$selection,r),s=this.options.get("dropdownAdapter"),this.dropdown=new s(e,this.options),this.$dropdown=this.dropdown.render(),this.dropdown.position(this.$dropdown,r),r=this.options.get("resultsAdapter"),this.results=new r(e,this.options,this.dataAdapter),this.$results=this.results.render(),this.results.position(this.$results,this.$dropdown);var o=this;this._bindAdapters(),this._registerDomEvents(),this._registerDataEvents(),this._registerSelectionEvents(),this._registerDropdownEvents(),this._registerResultsEvents(),this._registerEvents(),this.dataAdapter.current((function(e){o.trigger("selection:update",{data:e})})),e[0].classList.add("select2-hidden-accessible"),e.attr("aria-hidden","true"),this._syncAttributes(),n.StoreData(e[0],"select2",this),e.data("select2",this)};return n.Extend(i,n.Observable),i.prototype._generateId=function(e){return"select2-"+(null!=e.attr("id")?e.attr("id"):null!=e.attr("name")?e.attr("name")+"-"+n.generateChars(2):n.generateChars(4)).replace(/(:|\.|\[|\]|,)/g,"")},i.prototype._placeContainer=function(e){e.insertAfter(this.$element);var t=this._resolveWidth(this.$element,this.options.get("width"));null!=t&&e.css("width",t)},i.prototype._resolveWidth=function(e,t){var n=/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i;if("resolve"==t){var s=this._resolveWidth(e,"style");return null!=s?s:this._resolveWidth(e,"element")}if("element"==t)return(s=e.outerWidth(!1))<=0?"auto":s+"px";if("style"!=t)return"computedstyle"!=t?t:window.getComputedStyle(e[0]).width;if("string"!=typeof(e=e.attr("style")))return null;for(var i=e.split(";"),r=0,o=i.length;r<o;r+=1){var a=i[r].replace(/\s/g,"").match(n);if(null!==a&&1<=a.length)return a[1]}return null},i.prototype._bindAdapters=function(){this.dataAdapter.bind(this,this.$container),this.selection.bind(this,this.$container),this.dropdown.bind(this,this.$container),this.results.bind(this,this.$container)},i.prototype._registerDomEvents=function(){var e=this;this.$element.on("change.select2",(function(){e.dataAdapter.current((function(t){e.trigger("selection:update",{data:t})}))})),this.$element.on("focus.select2",(function(t){e.trigger("focus",t)})),this._syncA=n.bind(this._syncAttributes,this),this._syncS=n.bind(this._syncSubtree,this),this._observer=new window.MutationObserver((function(t){e._syncA(),e._syncS(t)})),this._observer.observe(this.$element[0],{attributes:!0,childList:!0,subtree:!1})},i.prototype._registerDataEvents=function(){var e=this;this.dataAdapter.on("*",(function(t,n){e.trigger(t,n)}))},i.prototype._registerSelectionEvents=function(){var e=this,t=["toggle","focus"];this.selection.on("toggle",(function(){e.toggleDropdown()})),this.selection.on("focus",(function(t){e.focus(t)})),this.selection.on("*",(function(n,s){-1===t.indexOf(n)&&e.trigger(n,s)}))},i.prototype._registerDropdownEvents=function(){var e=this;this.dropdown.on("*",(function(t,n){e.trigger(t,n)}))},i.prototype._registerResultsEvents=function(){var e=this;this.results.on("*",(function(t,n){e.trigger(t,n)}))},i.prototype._registerEvents=function(){var e=this;this.on("open",(function(){e.$container[0].classList.add("select2-container--open")})),this.on("close",(function(){e.$container[0].classList.remove("select2-container--open")})),this.on("enable",(function(){e.$container[0].classList.remove("select2-container--disabled")})),this.on("disable",(function(){e.$container[0].classList.add("select2-container--disabled")})),this.on("blur",(function(){e.$container[0].classList.remove("select2-container--focus")})),this.on("query",(function(t){e.isOpen()||e.trigger("open",{}),this.dataAdapter.query(t,(function(n){e.trigger("results:all",{data:n,query:t})}))})),this.on("query:append",(function(t){this.dataAdapter.query(t,(function(n){e.trigger("results:append",{data:n,query:t})}))})),this.on("keypress",(function(t){var n=t.which;e.isOpen()?n===s.ESC||n===s.UP&&t.altKey?(e.close(t),t.preventDefault()):n===s.ENTER||n===s.TAB?(e.trigger("results:select",{}),t.preventDefault()):n===s.SPACE&&t.ctrlKey?(e.trigger("results:toggle",{}),t.preventDefault()):n===s.UP?(e.trigger("results:previous",{}),t.preventDefault()):n===s.DOWN&&(e.trigger("results:next",{}),t.preventDefault()):(n===s.ENTER||n===s.SPACE||n===s.DOWN&&t.altKey)&&(e.open(),t.preventDefault())}))},i.prototype._syncAttributes=function(){this.options.set("disabled",this.$element.prop("disabled")),this.isDisabled()?(this.isOpen()&&this.close(),this.trigger("disable",{})):this.trigger("enable",{})},i.prototype._isChangeMutation=function(e){var t=this;if(e.addedNodes&&0<e.addedNodes.length){for(var n=0;n<e.addedNodes.length;n++)if(e.addedNodes[n].selected)return!0}else{if(e.removedNodes&&0<e.removedNodes.length)return!0;if(Array.isArray(e))return e.some((function(e){return t._isChangeMutation(e)}))}return!1},i.prototype._syncSubtree=function(e){e=this._isChangeMutation(e);var t=this;e&&this.dataAdapter.current((function(e){t.trigger("selection:update",{data:e})}))},i.prototype.trigger=function(e,t){var n=i.__super__.trigger;if(void 0===t&&(t={}),e in(r={open:"opening",close:"closing",select:"selecting",unselect:"unselecting",clear:"clearing"})){var s=r[e],r={prevented:!1,name:e,args:t};if(n.call(this,s,r),r.prevented)return void(t.prevented=!0)}n.call(this,e,t)},i.prototype.toggleDropdown=function(){this.isDisabled()||(this.isOpen()?this.close():this.open())},i.prototype.open=function(){this.isOpen()||this.isDisabled()||this.trigger("query",{})},i.prototype.close=function(e){this.isOpen()&&this.trigger("close",{originalEvent:e})},i.prototype.isEnabled=function(){return!this.isDisabled()},i.prototype.isDisabled=function(){return this.options.get("disabled")},i.prototype.isOpen=function(){return this.$container[0].classList.contains("select2-container--open")},i.prototype.hasFocus=function(){return this.$container[0].classList.contains("select2-container--focus")},i.prototype.focus=function(e){this.hasFocus()||(this.$container[0].classList.add("select2-container--focus"),this.trigger("focus",{}))},i.prototype.enable=function(e){this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("enable")` method has been deprecated and will be removed in later Select2 versions. Use $element.prop("disabled") instead.'),e=!(e=null==e||0===e.length?[!0]:e)[0],this.$element.prop("disabled",e)},i.prototype.data=function(){this.options.get("debug")&&0<arguments.length&&window.console&&console.warn&&console.warn('Select2: Data can no longer be set using `select2("data")`. You should consider setting the value instead using `$element.val()`.');var e=[];return this.dataAdapter.current((function(t){e=t})),e},i.prototype.val=function(e){if(this.options.get("debug")&&window.console&&console.warn&&console.warn('Select2: The `select2("val")` method has been deprecated and will be removed in later Select2 versions. Use $element.val() instead.'),null==e||0===e.length)return this.$element.val();e=e[0],Array.isArray(e)&&(e=e.map((function(e){return e.toString()}))),this.$element.val(e).trigger("input").trigger("change")},i.prototype.destroy=function(){n.RemoveData(this.$container[0]),this.$container.remove(),this._observer.disconnect(),this._observer=null,this._syncA=null,this._syncS=null,this.$element.off(".select2"),this.$element.attr("tabindex",n.GetData(this.$element[0],"old-tabindex")),this.$element[0].classList.remove("select2-hidden-accessible"),this.$element.attr("aria-hidden","false"),n.RemoveData(this.$element[0]),this.$element.removeData("select2"),this.dataAdapter.destroy(),this.selection.destroy(),this.dropdown.destroy(),this.results.destroy(),this.dataAdapter=null,this.selection=null,this.dropdown=null,this.results=null},i.prototype.render=function(){var t=e('<span class="select2 select2-container"><span class="selection"></span><span class="dropdown-wrapper" aria-hidden="true"></span></span>');return t.attr("dir",this.options.get("dir")),this.$container=t,this.$container[0].classList.add("select2-container--"+this.options.get("theme")),n.StoreData(t[0],"element",this.$element),t},i})),x.define("jquery-mousewheel",["jquery"],(function(e){return e})),x.define("jquery.select2",["jquery","jquery-mousewheel","./select2/core","./select2/defaults","./select2/utils"],(function(e,t,n,s,i){var r;return null==e.fn.select2&&(r=["open","close","destroy"],e.fn.select2=function(t){if("object"==typeof(t=t||{}))return this.each((function(){var s=e.extend(!0,{},t);new n(e(this),s)})),this;if("string"!=typeof t)throw new Error("Invalid arguments for Select2: "+t);var s,o=Array.prototype.slice.call(arguments,1);return this.each((function(){var e=i.GetData(this,"select2");null==e&&window.console&&console.error&&console.error("The select2('"+t+"') method was called on an element that is not using Select2."),s=e[t].apply(e,o)})),-1<r.indexOf(t)?this:s}),null==e.fn.select2.defaults&&(e.fn.select2.defaults=s),n})),{define:x.define,require:x.require})).require("jquery.select2");return e.fn.select2.amd=g,x}));