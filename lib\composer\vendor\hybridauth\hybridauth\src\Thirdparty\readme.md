##### Third party libraries

Here we include a number of third party libraries. Those libraries are used by the various providers supported by Hybridauth.

Library | Description
-------- | -------------
[LightOpenID](https://gitorious.org/lightopenid) | Contain LightOpenID. Solid OpenID library licensed under the MIT License.
[OAuth Library](https://code.google.com/p/oauth/) | Contain OAuth Library licensed under the MIT License.

Notes: 

    We no longer use the old OAuth clients. Please don't add new libs to this folder, unless strictly necessary.
    Both LightOpenID and OAuth are (to be) partially/indirectly tested within the Hybridauth library. 
    Both LightOpenID and OAuth libraries are excluded from Codeclimate.com Analysis/GPA.
