!function(){var t={76:function(){jQuery(document).ready((function(t){"use strict";function s(s,a,e,n){var i=this;t.ajax({type:"GET",url:s,beforeSend:function(){t(i).addClass("wslu-plugin-install-activate"),e&&t(i).html(e)},success:function(e){t(i).removeClass("wslu-plugin-install-activate"),s.indexOf("action=activate")>=0&&t(i).addClass("activated"),t(i).html(n),a&&a()}})}t("#wslu-admin-settings-form").on("submit",(function(s){var a=t(this),e=a.find(".wslu-admin-settings-form-submit"),n=a.serialize();a.addClass("is-loading"),e.attr("disabled",!0),e.find(".wslu-admin-save-icon").hide(),n+="&nonce="+ekit_ajax_var.nonce,t.post(ajaxurl+"?action=wp_social_admin_action",n,(function(t){a.removeClass("is-loading"),e.removeAttr("disabled"),e.find(".wslu-admin-save-icon").fadeIn(),e.hasClass("wslu-onboard-btn")&&(window.location.href=window.location.origin+window.location.pathname+"?page=wslu_global_setting")})),s.preventDefault()})),t(".wslu-onboard-step-wrapper #signup").on("input",(function(){var s,a,e;s=t(this),e=s,t(".error").remove(),!e.length||(a=e.val(),/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/.test(a))||e.parent().after('<p class="error">Please enter valid email.</p>')})),t(".wslu-onboard-step-wrapper #signup").on("keydown",(function(t){"Enter"==t.key&&t.preventDefault()})),t(".wslu-onboard-nav-item").on("click",(function(){if(t(this).index()>1&&!t(this).hasClass("next")&&!t(this).hasClass("selected"))return!1;if(t(this).hasClass("next"))t(".wslu-onboard-step-wrapper.active #signup");t(this).next().addClass("next").siblings().removeClass("next"),t(this).removeClass("selected"),t(this).addClass("active").siblings().removeClass("active"),t(this).prevAll().addClass("selected").end().nextAll().removeClass("selected");var s=t(this).data("step_key"),a=t(this).parents(".wslu-onboard-nav"),e=a.offset().left,n=t(this).is(":last-child")?a.width():t(this).offset().left-e+t(this).outerWidth();t(".wslu-onboard-progressbar").css("width",n),t(".wslu-onboard-"+s).addClass("active").siblings().removeClass("active")})),t(".wslu-onboard-nav-item:first-of-type").trigger("click"),t(".wslu-onboard-pagi-btn").on("click",(function(s){t(this).hasClass("next")&&t(".wslu-onboard-nav-item.active").next().trigger("click"),t(this).hasClass("prev")&&t(".wslu-onboard-nav-item.active").prev().trigger("click")})),t(".wslu-onboard-single-plugin--install_plugin").on("click",(function(a){var e=this;a.preventDefault();var n=t(this).attr("href"),i=t(this).attr("data-activation_url"),l=t(this).data("plugin_status");if(t(this).hasClass("wslu-plugin-install-activate")||t(this).hasClass("activated"))return!1;"not_installed"==l?s.call(this,n,(function(){s.call(e,i,null,"Activating...","Activated")}),"Installing...","Installed"):"installed"==l&&s.call(this,i,null,"Activating...","Activated")})),jQuery(".wslu-onboard-tut-term--help").on("click",(function(){t(this).toggleClass("active").prev().toggleClass("active")}))}))}},s={};function a(e){var n=s[e];if(n!==undefined)return n.exports;var i=s[e]={exports:{}};return t[e](i,i.exports,a),i.exports}a.n=function(t){var s=t&&t.__esModule?function(){return t["default"]}:function(){return t};return a.d(s,{a:s}),s},a.d=function(t,s){for(var e in s)a.o(s,e)&&!a.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:s[e]})},a.o=function(t,s){return Object.prototype.hasOwnProperty.call(t,s)},function(){"use strict";a(76)}()}();