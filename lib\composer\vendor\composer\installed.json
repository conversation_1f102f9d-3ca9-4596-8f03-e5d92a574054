{"packages": [{"name": "hybridauth/hybridauth", "version": "v3.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/hybridauth/hybridauth.git", "reference": "b70b9cbe1c7fa99b5847c5868610a8f024cc7d38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hybridauth/hybridauth/zipball/b70b9cbe1c7fa99b5847c5868610a8f024cc7d38", "reference": "b70b9cbe1c7fa99b5847c5868610a8f024cc7d38", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^6.5 || ^8.0"}, "suggest": {"firebase/php-jwt": "Needed to support Apple provider", "phpseclib/phpseclib": "Needed to support Apple provider"}, "time": "2024-01-02T23:09:56+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Hybridauth\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Miled", "email": "<EMAIL>"}], "description": "PHP Social Authentication Library", "homepage": "https://hybridauth.github.io", "keywords": ["Authentication", "OpenId", "api", "apple", "authorization", "facebook", "google", "o<PERSON>h", "social", "twitter"], "support": {"gitter": "https://gitter.im/hybridauth/hybridauth", "issues": "https://github.com/hybridauth/hybridauth/issues", "source": "https://github.com/hybridauth/hybridauth/tree/v3.11.0"}, "install-path": "../hybridauth/hybridauth"}, {"name": "paragonie/constant_time_encoding", "version": "v3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/df1e7fde177501eee2037dd159cf04f5f301a512", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512", "shasum": ""}, "require": {"php": "^8"}, "require-dev": {"phpunit/phpunit": "^9", "vimeo/psalm": "^4|^5"}, "time": "2024-05-08T12:36:18+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "install-path": "../paragonie/constant_time_encoding"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "version_normalized": "**********", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "time": "2020-10-15T08:29:30+00:00", "type": "library", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "install-path": "../paragonie/random_compat"}, {"name": "phpseclib/phpseclib", "version": "3.0.41", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "621c73f7dcb310b61de34d1da4c4204e8ace6ceb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/621c73f7dcb310b61de34d1da4c4204e8ace6ceb", "reference": "621c73f7dcb310b61de34d1da4c4204e8ace6ceb", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2|^3", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "time": "2024-08-12T00:13:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.41"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "install-path": "../phpseclib/phpseclib"}], "dev": true, "dev-package-names": []}