# WP Social Plugin Documentation

## Overview

**WP Social** is a comprehensive WordPress plugin that provides social media integration features including social login, social sharing, and social counter functionality. Developed by Wpmet, this plugin allows website owners to integrate multiple social media platforms seamlessly into their WordPress sites.

**Version:** 3.1.2  
**Author:** Wpmet  
**License:** GPLv3  
**Minimum PHP:** 7.4  
**WordPress Compatibility:** 5.0 - 6.8  

## Core Features

### 1. Social Login/Register
- **Supported Providers:** Facebook, Google, LinkedIn, Twitter, Dribbble, GitHub, WordPress, Vkontakte, Reddit, LineApp
- **Integration Points:** WordPress login, WooCommerce, BuddyPress, comment forms
- **Features:**
  - Custom login redirect URLs
  - User profile synchronization
  - Email notifications for new registrations
  - GDPR compliant user consent

### 2. Social Share
- **Supported Platforms:** Facebook, Twitter, LinkedIn, Pinterest, WhatsApp, Telegram, Email, Reddit, Digg, StumbleUpon, Facebook Messenger, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>iber
- **Features:**
  - Multiple display styles and layouts
  - Horizontal and vertical orientations
  - Share count display (optional)
  - Custom positioning on posts/pages
  - Shortcode support

### 3. Social Counter
- **Supported Platforms:** Facebook, Twitter, Pinterest, Dribbble, Instagram, YouTube, Mailchimp, Comments, Posts
- **Features:**
  - Real-time follower/subscriber counts
  - Customizable cache duration
  - Multiple display styles and hover effects
  - Manual cache clearing
  - API-based data fetching

## Technical Architecture

### Framework and Libraries

#### Core Dependencies
- **HybridAuth 3.11.0+** - OAuth authentication library for social login
- **PHPSecLib 3.0+** - Cryptographic library for secure communications
- **WordPress REST API** - For AJAX operations and API endpoints
- **Elementor Integration** - Custom widgets for page builders

#### Frontend Technologies
- **jQuery** - JavaScript framework for UI interactions
- **Select2** - Enhanced select dropdowns
- **SortableJS** - Drag-and-drop functionality for provider ordering
- **CSS3** - Modern styling with responsive design

#### Backend Architecture
- **Namespace:** `WP_Social`
- **Autoloader:** PSR-4 compliant autoloading system
- **Singleton Pattern:** Used for core classes
- **Factory Pattern:** Provider instantiation (Share_Factory, Counter_Factory)
- **Observer Pattern:** WordPress hooks and filters

### Directory Structure

```
wp-social/
├── app/                    # Core application logic
│   ├── api-routes.php     # REST API endpoints
│   ├── providers.php      # Social provider configurations
│   ├── settings.php       # Settings management
│   └── ...
├── base/                  # Abstract base classes
├── helper/                # Utility functions and helpers
├── inc/                   # WordPress integration files
│   ├── elementor/         # Elementor widget integration
│   ├── admin-*.php        # Admin interface files
│   └── ...
├── lib/                   # External libraries and components
│   ├── composer/          # Composer dependencies
│   ├── provider/          # Provider-specific implementations
│   ├── counter/           # Counter functionality
│   └── ...
├── assets/                # Frontend assets (CSS, JS, images)
├── template/              # Template files for rendering
└── xs_migration/          # Database migration system
```

## API Endpoints

### REST API Routes

#### Social Provider Management
- **POST** `/wp-json/wp_social/v1/counter/enable/{provider}` - Enable/disable counter provider
- **POST** `/wp-json/wp_social/v1/share/enable/{provider}` - Enable/disable share provider  
- **POST** `/wp-json/wp_social/v1/login/enable/{provider}` - Enable/disable login provider

#### Share Tracking
- **POST** `/wp-json/wp_social/v1/shared/url` - Track share count for URLs

#### Legacy Endpoints
- **GET** `/wp-json/wslu-social-login/type/{provider}` - Social login callback
- **POST** `/wp-json/wslu/v1/check_cache/{type}` - Check cache expiration
- **POST** `/wp-json/wslu/v1/save_cache/{type}` - Save counter cache data

### Authentication & Security
- **Nonce Verification:** All admin endpoints require valid WordPress nonces
- **Capability Checks:** Admin operations require `manage_options` capability
- **CSRF Protection:** REST API endpoints use WordPress nonce system
- **Data Sanitization:** All input data is sanitized and validated

## Integration Points

### WordPress Core
- **Hooks:** Integrates with WordPress login, registration, and user management
- **Widgets:** Provides WordPress widgets for counter, share, and login
- **Shortcodes:** Supports shortcode embedding in posts and pages
- **Admin Menu:** Dedicated admin interface under "WP Social" menu

### Third-Party Integrations

#### WooCommerce
- Social login on checkout and account pages
- Customer registration via social accounts
- Order completion social sharing

#### Elementor
- Custom Elementor widgets for social features
- Live preview in Elementor editor
- Style customization through Elementor interface

#### BuddyPress
- Social login integration with BuddyPress registration
- Profile synchronization with social media accounts

### Apple Integration
**Note:** The plugin does not currently include native Apple Sign-In integration. Apple integration would require:
- Apple Developer account setup
- Sign in with Apple service configuration
- Additional OAuth provider implementation
- iOS app integration (if applicable)

## Configuration Requirements

### Social Login Setup
Each social provider requires API credentials:

1. **Facebook:** App ID, App Secret, Redirect URI
2. **Google:** Client ID, Client Secret, Redirect URI  
3. **Twitter:** Consumer Key, Consumer Secret
4. **LinkedIn:** Client ID, Client Secret
5. **GitHub:** Client ID, Client Secret
6. **And others...**

### Social Counter APIs
Some providers require API keys:
- **YouTube:** YouTube Data API v3 key
- **Twitter:** Twitter API credentials
- **Dribbble:** Client ID and Secret
- **Mailchimp:** API key and List ID

## Performance Features

### Caching System
- **Counter Caching:** Configurable cache duration (hours)
- **Manual Cache Clearing:** Admin option to force cache refresh
- **Transient API:** Uses WordPress transients for temporary data storage

### Optimization
- **Conditional Loading:** Scripts load only when needed
- **Minified Assets:** Compressed CSS and JavaScript files
- **Lazy Loading:** Social counters load asynchronously

## Security Features

- **OAuth 2.0 Compliance:** Secure authentication protocols
- **Data Encryption:** Sensitive data encrypted using PHPSecLib
- **Input Validation:** All user inputs sanitized and validated
- **GDPR Compliance:** User consent mechanisms for data collection
- **Regular Security Updates:** Active maintenance and security patches

## Upcoming Features & Roadmap

Based on the plugin's development pattern and user requests:

### Planned APIs
- Enhanced Instagram Business API integration
- TikTok social login and counter support
- Discord integration
- Snapchat sharing capabilities
- Apple Sign-In implementation

### Feature Enhancements
- Advanced analytics and reporting
- Custom social provider support
- Multi-site network compatibility
- Advanced styling options
- Performance optimizations

## Support and Documentation

- **Documentation:** [https://help.wpmet.com/docs/wp-social/](https://help.wpmet.com/docs/wp-social/)
- **Support:** [https://wpmet.com/support-ticket-form/](https://wpmet.com/support-ticket-form/)
- **Community:** [Facebook Group](https://www.facebook.com/groups/wpmet/)
- **Video Tutorials:** [YouTube Channel](https://www.youtube.com/c/Wpmet/videos)

## Pro Version

The plugin offers a Pro version with additional features:
- Advanced styling options
- Priority support
- Additional social providers
- Enhanced analytics
- Custom branding options

For more information: [https://wpmet.com/plugin/wp-social/](https://wpmet.com/plugin/wp-social/)
