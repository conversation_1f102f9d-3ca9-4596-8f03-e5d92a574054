<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="WordPress Coding Standards" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/squizlabs/PHP_CodeSniffer/master/phpcs.xsd">

	<description>The Coding standard for the WordPress Coding Standards itself.</description>

	<file>.</file>

	<arg value="sp"/>
	<arg name="extensions" value="php"/>
	<arg name="basepath" value="."/>
	<arg name="parallel" value="8"/>
	<!-- <arg name="cache" value="false"/> -->
    <!-- <arg name="standard" value="WordPress"/> -->

	<exclude-pattern>dev-*</exclude-pattern>
	<exclude-pattern>node_modules/*</exclude-pattern>
	<exclude-pattern>vendor/*</exclude-pattern>

	<!-- <rule ref="WordPress"> -->
		<!-- <exclude name="WordPress.Files.FileName"/> -->
		<!-- <exclude name="WordPress.NamingConventions.ValidVariableName"/> -->
		<!-- <exclude name="WordPress.CodeAnalysis.AssignmentInCondition.FoundInWhileCondition"/> -->
	<!-- </rule> -->

    <rule ref="WordPress.Security">
        <exclude name="Squiz.WhiteSpace.SuperfluousWhitespace.EndLine"/>
		<exclude name="WordPress.Files.FileName"/>
		<exclude name="WordPress.Arrays"/>
		<exclude name="WordPress.NamingConventions.ValidVariableName"/>
		<exclude name="WordPress.CodeAnalysis.AssignmentInCondition.FoundInWhileCondition"/>
    </rule>
	<!-- Enforce PSR1 compatible namespaces. -->
	<!-- <rule ref="PSR1.Classes.ClassDeclaration"/> -->

	<!-- <rule ref="WordPress.Arrays.MultipleStatementAlignment"> -->
		<!-- <properties> -->
			<!-- <property name="alignMultilineItems" value="!=100"/> -->
			<!-- <property name="exact" value="false" phpcs-only="true"/> -->
		<!-- </properties> -->
	<!-- </rule> -->

	<rule ref="PSR2.Methods.FunctionClosingBrace"/>

	<!-- Check code for cross-version PHP compatibility. -->
	<!-- <config name="testVersion" value="5.4-"/> -->
	<!-- <rule ref="PHPCompatibility"> -->
		<!-- Exclude PHP constants back-filled by PHPCS. -->
		<!-- <exclude name="PHPCompatibility.Constants.NewConstants.t_finallyFound"/> -->
		<!-- <exclude name="PHPCompatibility.Constants.NewConstants.t_yieldFound"/> -->
		<!-- <exclude name="PHPCompatibility.Constants.NewConstants.t_ellipsisFound"/> -->
		<!-- <exclude name="PHPCompatibility.Constants.NewConstants.t_powFound"/> -->
		<!-- <exclude name="PHPCompatibility.Constants.NewConstants.t_pow_equalFound"/> -->
		<!-- <exclude name="PHPCompatibility.Constants.NewConstants.t_spaceshipFound"/> -->
		<!-- <exclude name="PHPCompatibility.Constants.NewConstants.t_coalesceFound"/> -->
		<!-- <exclude name="PHPCompatibility.Constants.NewConstants.t_coalesce_equalFound"/> -->
		<!-- <exclude name="PHPCompatibility.Constants.NewConstants.t_yield_fromFound"/> -->
	<!-- </rule> -->

	<!--
	#############################################################################
	Handbook: PHP - Formatting SQL statements.
	Ref: https://make.wordpress.org/core/handbook/best-practices/coding-standards/php/#formatting-sql-statements
	#############################################################################
	-->

	<!-- Rule: in $wpdb->prepare - %s is used for string placeholders and %d is used for integer
		 placeholders. Note that they are not 'quoted'! -->
	<rule ref="WordPress.DB.PreparedSQLPlaceholders"/>
	<rule ref="WordPress.DB.DirectDatabaseQuery" />

	<!-- Covers rule:  $wpdb->prepare()... The benefit of this is that we don't have to remember
		 to manually use esc_sql(), and also that it is easy to see at a glance whether something
		 has been escaped or not, because it happens right when the query happens. -->
	<rule ref="WordPress.DB.PreparedSQL"/>

	<!-- Covers rule: Avoid touching the database directly. -->
	<rule ref="WordPress.DB.RestrictedFunctions"/>
	<rule ref="WordPress.DB.RestrictedClasses"/>

</ruleset>