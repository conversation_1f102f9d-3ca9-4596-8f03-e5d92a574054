(()=>{var e={942:(e,t)=>{var a;!function(){"use strict";var r={}.hasOwnProperty;function n(){for(var e="",t=0;t<arguments.length;t++){var a=arguments[t];a&&(e=i(e,l(a)))}return e}function l(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return n.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var a in e)r.call(e,a)&&e[a]&&(t=i(t,a));return t}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(n["default"]=n,e.exports=n):void 0===(a=function(){return n}.apply(t,[]))||(e.exports=a)}()}},t={};function a(r){var n=t[r];if(void 0!==n)return n.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,a),l.exports}a.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e;a.g.importScripts&&(e=a.g.location+"");var t=a.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var n=r.length-1;n>-1&&(!e||!/^http(s?):/.test(e));)e=r[n--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),a.p=e+"../"})(),(()=>{"use strict";const e=window.React,t=window.wp.element,r=window.wp.components;var n=a(942),l=a.n(n);const i=(0,t.createContext)(null),s=()=>{const e=(0,t.useContext)(i);if(!e)throw new Error("useContextLibrary must be used within a TemplateLibraryProvider");return e},o=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"188",height:"24",viewBox:"0 0 188 24",fill:"none"},(0,e.createElement)("path",{d:"M21.3005 7.51235C20.9252 7.23057 20.3617 7.13704 19.8928 7.23057C19.424 7.3241 18.9539 7.60588 18.6733 8.07472C17.3591 10.1395 14.2631 10.2331 14.2631 10.2331H14.1696C9.28931 10.2331 7.41274 14.4562 7.31921 14.6433C6.9439 15.5822 7.31921 16.7081 8.25809 17.0834C8.44515 17.177 8.72694 17.2717 9.00872 17.2717C9.75935 17.2717 10.4164 16.8029 10.7918 16.1458C11.2606 15.3016 11.9177 14.6445 12.8566 14.2692V17.647C12.7631 18.3041 12.3877 18.8665 11.8242 19.2418C11.1671 19.7106 10.1347 19.9924 8.91519 19.9924C7.50747 19.9924 6.38153 19.5236 5.53737 18.5847C4.69322 17.6458 4.22318 16.1446 4.22318 14.268V9.20066C4.3167 7.51115 4.69202 6.29168 5.53737 5.44753C6.38153 4.50864 7.50747 4.0398 8.91519 4.0398C10.1347 4.0398 11.0735 4.32159 11.8242 4.79043C12.3877 5.16574 12.7631 5.82284 12.8566 6.57347V6.667C12.8566 7.88647 13.7955 8.82535 15.0149 8.82535C16.2344 8.82535 17.1733 7.88647 17.1733 6.667V6.19815C17.0798 5.25927 16.798 4.41512 16.3291 3.66449C15.8603 2.91386 15.2967 2.16323 14.5461 1.69439C13.0448 0.568452 11.1683 0.00488281 8.91519 0.00488281C6.288 0.00488281 4.12965 0.943766 2.44014 2.63208C0.938883 4.32159 0.0935286 6.47994 0 9.01361V14.1745C0 16.9899 0.844156 19.3353 2.44014 21.1184C4.03492 22.9014 6.288 23.7456 8.91519 23.7456C11.0735 23.7456 12.9501 23.182 14.4514 22.1508C15.8591 21.2119 16.7968 19.7106 16.985 18.0223L17.0786 13.4238C18.5798 12.955 20.4564 12.0161 21.677 10.1395C21.9588 9.76423 22.1459 9.20066 22.0524 8.73182C22.0524 8.26298 21.677 7.79294 21.3017 7.51235H21.3005Z",fill:"url(#paint0_linear_3624_623)"}),(0,e.createElement)("path",{d:"M12.8542 6.3867C12.8542 7.60617 13.7931 8.63858 15.0125 8.63858C16.232 8.63858 17.1709 7.69969 17.1709 6.48023V6.19844C17.0774 5.25956 16.7956 4.4154 16.3267 3.66477C15.8579 2.91415 15.4826 2.44531 14.5437 1.69468C12.1036 -0.181889 8.9128 0.00516842 8.9128 0.00516842C12.1036 1.22464 12.8542 5.26076 12.8542 6.3867Z",fill:"url(#paint1_linear_3624_623)"}),(0,e.createElement)("path",{d:"M21.3005 7.51264C20.9252 7.23085 20.3616 7.13733 19.8928 7.23086C19.424 7.32438 18.9539 7.60617 18.6733 8.07501C17.3591 10.1398 14.2631 10.2334 14.2631 10.2334H14.1696C9.2893 10.2334 7.41274 14.4565 7.31921 14.6436C6.94389 15.5825 7.31921 16.7084 8.25809 17.0837C8.44515 17.1773 8.72693 17.272 9.00872 17.272C9.75934 17.272 10.4164 16.8031 10.7918 16.146C10.7918 16.0525 11.5424 14.7383 12.8566 14.2695C14.4514 13.7059 15.202 14.176 17.1733 13.5189C18.6745 13.05 20.5511 12.1111 21.7718 10.2346C22.0536 9.85925 22.2406 9.29568 22.1471 8.82684C22.0536 8.26327 21.6782 7.79442 21.3029 7.51264H21.3005Z",fill:"url(#paint2_linear_3624_623)"}),(0,e.createElement)("path",{d:"M21.3005 7.51264C20.9252 7.23085 20.3616 7.13733 19.8928 7.23086C19.424 7.32438 18.9539 7.60617 18.6733 8.07501C17.3591 10.1398 14.1684 10.2334 14.1684 10.2334C17.4527 9.66979 18.8604 14.4565 21.7694 10.2334C22.0512 9.85805 22.2382 9.29448 22.1447 8.82564C22.0512 8.26207 21.6758 7.79323 21.3005 7.51144V7.51264Z",fill:"url(#paint3_linear_3624_623)"}),(0,e.createElement)("path",{d:"M24 4.37354H35.94V6.90757H31.4898V18.9114H28.4512V6.90757H24.001V4.37354H24Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M35.8258 13.5092C35.8258 10.1442 37.9058 7.86621 41.1138 7.86621C43.9952 7.86621 46.2389 9.69772 46.2389 13.4026V14.2327H38.8139V14.2403C38.8139 15.8662 39.7725 16.8667 41.2842 16.8667C42.2922 16.8667 43.0728 16.4336 43.3784 15.6035L46.1752 15.7882C45.7496 17.811 43.9324 19.1247 41.2346 19.1247C37.8772 19.1247 35.8258 16.9666 35.8258 13.5102V13.5092ZM43.3927 12.3593C43.386 11.0599 42.4769 10.1232 41.1633 10.1232C39.8496 10.1232 38.8777 11.117 38.8139 12.3593H43.3927Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M48.2123 8.00805H51.0947V9.93189H51.2223C51.6763 8.64679 52.791 7.86621 54.2608 7.86621C55.7306 7.86621 56.8729 8.66869 57.2213 9.93189H57.3346C57.782 8.68296 59.0243 7.86621 60.6216 7.86621C62.6521 7.86621 64.0571 9.21509 64.0571 11.5787V18.9114H61.0404V12.1746C61.0404 10.9609 60.316 10.3574 59.3508 10.3574C58.2503 10.3574 57.6049 11.1237 57.6049 12.2527V18.9114H54.673V12.1109C54.673 11.039 54.0057 10.3574 53.0119 10.3574C52.0181 10.3574 51.2375 11.1599 51.2375 12.366V18.9105H48.2132V8.0071L48.2123 8.00805Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M66.4417 8.00805H69.4231V9.83956H69.5583C69.9629 8.95236 70.8358 7.86621 72.6606 7.86621C75.0528 7.86621 77.0757 9.72628 77.0757 13.474C77.0757 17.2218 75.1375 19.0885 72.653 19.0885C70.8929 19.0885 69.9771 18.0737 69.5583 17.1646H69.466V23H66.4417V8.00805ZM71.6944 16.682C73.1775 16.682 73.9876 15.3617 73.9876 13.4597C73.9876 11.5578 73.1928 10.2727 71.6944 10.2727C70.1961 10.2727 69.4012 11.5149 69.4012 13.4597C69.4012 15.4045 70.2389 16.682 71.6944 16.682Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M82.1085 18.9104H79.0842V4.37354H82.1085V18.9114V18.9104Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M84.0105 15.8652C84.0105 13.4302 85.9486 12.7067 88.1066 12.5078C90.0019 12.3231 90.7472 12.2308 90.7472 11.5283V11.4854C90.7472 10.5906 90.158 10.0728 89.129 10.0728C88.0428 10.0728 87.3974 10.6049 87.1908 11.3293L84.3941 11.1018C84.8129 9.11416 86.5312 7.86523 89.1432 7.86523C91.5707 7.86523 93.7715 8.95805 93.7715 11.5568V18.9105H90.9034V17.3988H90.8186C90.2865 18.4135 89.2432 19.117 87.6316 19.117C85.5516 19.117 84.0114 18.0242 84.0114 15.8662L84.0105 15.8652ZM90.7682 15.0133V13.8567C90.4065 14.0985 89.327 14.2546 88.6673 14.346C87.6021 14.4954 86.8996 14.9143 86.8996 15.7653C86.8996 16.6163 87.5669 17.0285 88.4969 17.0285C89.8248 17.0285 90.7682 16.1556 90.7682 15.0123V15.0133Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M101.92 10.2794H99.8686V15.5607C99.8686 16.3984 100.287 16.6259 100.891 16.6259C101.175 16.6259 101.495 16.5545 101.643 16.5269L102.119 18.7772C101.813 18.8762 101.26 19.0333 100.479 19.0609C98.2856 19.1533 96.8301 18.0957 96.8444 15.9586V10.2794H95.3536V8.00809H96.8444V5.396H99.8686V8.00809H101.92V10.2794Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M103.333 13.5092C103.333 10.1442 105.413 7.86621 108.621 7.86621C111.502 7.86621 113.746 9.69772 113.746 13.4026V14.2327H106.321V14.2403C106.321 15.8662 107.279 16.8667 108.791 16.8667C109.799 16.8667 110.58 16.4336 110.885 15.6035L113.682 15.7882C113.257 17.811 111.439 19.1247 108.742 19.1247C105.384 19.1247 103.333 16.9666 103.333 13.5102V13.5092ZM110.9 12.3593C110.893 11.0599 109.984 10.1232 108.67 10.1232C107.356 10.1232 106.385 11.117 106.321 12.3593H110.9Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M120.412 4.37354H123.486V16.3773H129.718V18.9114H120.412V4.37354Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M131.699 8.00806H134.723V18.9114H131.699V8.00806Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M137.193 4.37354H140.217V9.8395H140.309C140.714 8.95231 141.587 7.86616 143.412 7.86616C145.804 7.86616 147.827 9.72622 147.827 13.474C147.827 17.2217 145.889 19.0884 143.404 19.0884C141.644 19.0884 140.728 18.0737 140.309 17.1646H140.174V18.9104H137.193V4.37354ZM142.446 16.682C143.929 16.682 144.739 15.3616 144.739 13.4597C144.739 11.5577 143.944 10.2726 142.446 10.2726C140.947 10.2726 140.152 11.5149 140.152 13.4597C140.152 15.4045 140.99 16.682 142.446 16.682Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M149.835 8.00817H152.767V9.91012H152.881C153.278 8.56124 154.279 7.85205 155.5 7.85205C155.806 7.85205 156.182 7.89489 156.451 7.95867V10.6422C156.168 10.5498 155.593 10.4784 155.174 10.4784C153.839 10.4784 152.86 11.4018 152.86 12.7431V18.9115H149.835V8.00817Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M157.19 15.8652C157.19 13.4302 159.128 12.7067 161.286 12.5078C163.181 12.3231 163.927 12.2308 163.927 11.5283V11.4854C163.927 10.5906 163.337 10.0728 162.308 10.0728C161.222 10.0728 160.577 10.6049 160.37 11.3293L157.574 11.1018C157.992 9.11416 159.711 7.86523 162.323 7.86523C164.75 7.86523 166.951 8.95805 166.951 11.5568V18.9105H164.083V17.3988H163.998C163.466 18.4135 162.423 19.117 160.811 19.117C158.731 19.117 157.191 18.0242 157.191 15.8662L157.19 15.8652ZM163.948 15.0133V13.8567C163.586 14.0985 162.506 14.2546 161.847 14.346C160.782 14.4954 160.079 14.9143 160.079 15.7653C160.079 16.6163 160.746 17.0285 161.676 17.0285C163.004 17.0285 163.948 16.1556 163.948 15.0123V15.0133Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M169.3 8.00817H172.232V9.91012H172.346C172.743 8.56124 173.744 7.85205 174.965 7.85205C175.271 7.85205 175.647 7.89489 175.916 7.95867V10.6422C175.633 10.5498 175.058 10.4784 174.639 10.4784C173.304 10.4784 172.325 11.4018 172.325 12.7431V18.9115H169.3V8.00817Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M177.776 22.6868L178.458 20.4297C179.523 20.7562 180.282 20.7067 180.68 19.6844L180.857 19.2227L176.945 8.00708H180.126L182.383 16.0137H182.496L184.775 8.00708H187.976L183.738 20.0889C183.12 21.8491 181.929 22.999 179.678 22.999C178.912 22.999 178.208 22.8714 177.775 22.6868H177.776Z",fill:"#12141C"}),(0,e.createElement)("path",{d:"M133.231 3C132.337 3 131.592 3.67016 131.592 4.48977C131.592 5.30938 132.337 6.05378 133.231 6.05378C134.125 6.05378 134.869 5.38363 134.869 4.48977C134.869 3.59591 134.125 3 133.231 3Z",fill:"url(#paint4_linear_3624_623)"}),(0,e.createElement)("defs",null,(0,e.createElement)("linearGradient",{id:"paint0_linear_3624_623",x1:"-0.00119908",y1:"11.8758",x2:"22.1303",y2:"11.8758",gradientUnits:"userSpaceOnUse"},(0,e.createElement)("stop",{stopColor:"#007BFF"}),(0,e.createElement)("stop",{offset:"1",stopColor:"#221377"})),(0,e.createElement)("linearGradient",{id:"paint1_linear_3624_623",x1:"12.9597",y1:"3.03765",x2:"17.6457",y2:"9.76811",gradientUnits:"userSpaceOnUse"},(0,e.createElement)("stop",{stopColor:"#007BFF"}),(0,e.createElement)("stop",{offset:"1",stopColor:"#221377"})),(0,e.createElement)("linearGradient",{id:"paint2_linear_3624_623",x1:"8.69216",y1:"15.9206",x2:"20.9192",y2:"7.9539",gradientUnits:"userSpaceOnUse"},(0,e.createElement)("stop",{stopColor:"#FC9257"}),(0,e.createElement)("stop",{offset:"1",stopColor:"#F05A24"})),(0,e.createElement)("linearGradient",{id:"paint3_linear_3624_623",x1:"19.8496",y1:"9.29928",x2:"17.1517",y2:"11.0403",gradientUnits:"userSpaceOnUse"},(0,e.createElement)("stop",{stopColor:"#FFA44D"}),(0,e.createElement)("stop",{offset:"1",stopColor:"#FBD06A"})),(0,e.createElement)("linearGradient",{id:"paint4_linear_3624_623",x1:"133.231",y1:"3.01809",x2:"133.231",y2:"6.07187",gradientUnits:"userSpaceOnUse"},(0,e.createElement)("stop",{stopColor:"#FC9257"}),(0,e.createElement)("stop",{offset:"1",stopColor:"#F05A24"})))),c=()=>(0,e.createElement)("svg",{width:"18",height:"15",viewBox:"0 0 18 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M1 1.72552V6.08916H5.36364",stroke:"#ABACAF",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,e.createElement)("path",{d:"M17.0001 13.3619V8.99823H12.6365",stroke:"#ABACAF",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,e.createElement)("path",{d:"M15.1745 5.36192C14.8057 4.31958 14.1788 3.38766 13.3524 2.65313C12.526 1.91859 11.5269 1.40538 10.4485 1.16138C9.3701 0.917371 8.24745 0.950528 7.18532 1.25775C6.12318 1.56498 5.15618 2.13625 4.37455 2.91828L1 6.08919M17 8.99828L13.6255 12.1692C12.8438 12.9512 11.8768 13.5225 10.8147 13.8297C9.75255 14.1369 8.6299 14.1701 7.55148 13.9261C6.47307 13.6821 5.47404 13.1689 4.64761 12.4343C3.82119 11.6998 3.1943 10.7679 2.82545 9.72555",stroke:"#ABACAF",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})),C=()=>(0,e.createElement)("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M13 1L1 13",stroke:"#ABACAF",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,e.createElement)("path",{d:"M1 1L13 13",stroke:"#ABACAF",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})),p=({onChange:a,onClick:r,onMouseLeave:n,onClose:l,className:i,placeholder:o})=>{const{keyWords:c,dispatch:C}=s(),p=(0,t.useCallback)((e=>{const t=e.target.value;C({type:"SET_KEY_WORDS",keyWords:t}),a(t)}),[a]);return(0,e.createElement)("div",{className:`search-container ${i}`},(0,e.createElement)("input",{type:"search",value:c,onChange:p,onClick:r,onMouseLeave:n,placeholder:o}),(0,e.createElement)("span",{className:"icon",onClick:()=>{C({type:"SET_KEY_WORDS",keyWords:""}),l()}},""===c?(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M13 5c-3.3 0-6 2.7-6 6 0 1.4.5 2.7 1.3 3.7l-3.8 3.8 1.1 1.1 3.8-3.8c1 .8 2.3 1.3 3.7 1.3 3.3 0 6-2.7 6-6S16.3 5 13 5zm0 10.5c-2.5 0-4.5-2-4.5-4.5s2-4.5 4.5-4.5 4.5 2 4.5 4.5-2 4.5-4.5 4.5z"})):(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"}))))};function m(){return(0,e.createElement)("svg",{className:"sync",xmlns:"http://www.w3.org/2000/svg",width:"100",height:"44",viewBox:"0 0 100 44",fill:"none"},(0,e.createElement)("g",{filter:"url(#filter0_d_1707_1883)"},(0,e.createElement)("path",{d:"M92 29C92 30.6569 90.6569 32 89 32H11C9.34315 32 8 30.6569 8 29V13C8 11.3431 9.34314 10 11 10H42.293C43.3468 10 44.3579 9.58421 45.1068 8.84297L50 4L54.8932 8.84297C55.6421 9.58421 56.6532 10 57.707 10H89C90.6569 10 92 11.3431 92 13V29Z",fill:"#111722"})),(0,e.createElement)("path",{d:"M19.9946 22.6196C19.9946 22.4292 19.965 22.2599 19.9058 22.1118C19.8507 21.9637 19.7513 21.8283 19.6074 21.7056C19.4635 21.5828 19.2604 21.4644 18.998 21.3501C18.7399 21.2316 18.4098 21.111 18.0078 20.9883C17.5677 20.8529 17.1615 20.7026 16.7891 20.5376C16.4209 20.3683 16.0993 20.1737 15.8242 19.9536C15.5492 19.7293 15.3354 19.4733 15.1831 19.1855C15.0308 18.8936 14.9546 18.5571 14.9546 18.1763C14.9546 17.7996 15.0329 17.4569 15.1895 17.1479C15.3503 16.839 15.5767 16.5724 15.8687 16.3481C16.1649 16.1196 16.514 15.944 16.916 15.8213C17.318 15.6943 17.7624 15.6309 18.249 15.6309C18.9346 15.6309 19.5249 15.7578 20.02 16.0117C20.5194 16.2656 20.9023 16.6063 21.1689 17.0337C21.4398 17.4611 21.5752 17.9329 21.5752 18.4492H19.9946C19.9946 18.1445 19.929 17.8758 19.7979 17.6431C19.6709 17.4061 19.4762 17.2199 19.2139 17.0845C18.9557 16.9491 18.6278 16.8813 18.23 16.8813C17.8534 16.8813 17.5402 16.9385 17.2905 17.0527C17.0409 17.167 16.8547 17.3215 16.7319 17.5161C16.6092 17.7108 16.5479 17.9308 16.5479 18.1763C16.5479 18.3498 16.5881 18.5085 16.6685 18.6523C16.7489 18.792 16.8716 18.9232 17.0366 19.0459C17.2017 19.1644 17.409 19.2765 17.6587 19.3823C17.9084 19.4881 18.2025 19.5897 18.541 19.687C19.0531 19.8394 19.4995 20.0086 19.8804 20.1948C20.2612 20.3768 20.5786 20.5841 20.8325 20.8169C21.0864 21.0496 21.2769 21.3141 21.4038 21.6104C21.5308 21.9023 21.5942 22.2345 21.5942 22.6069C21.5942 22.9963 21.516 23.3475 21.3594 23.6606C21.2028 23.9696 20.9785 24.234 20.6865 24.4541C20.3988 24.6699 20.0518 24.8371 19.6455 24.9556C19.2435 25.0698 18.7949 25.127 18.2998 25.127C17.8555 25.127 17.4175 25.0677 16.9858 24.9492C16.5584 24.8307 16.1691 24.6509 15.8179 24.4097C15.4666 24.1642 15.1873 23.8595 14.98 23.4956C14.7726 23.1274 14.6689 22.6979 14.6689 22.207H16.2622C16.2622 22.5075 16.313 22.7635 16.4146 22.9751C16.5203 23.1867 16.6663 23.3602 16.8525 23.4956C17.0387 23.6268 17.2546 23.7241 17.5 23.7876C17.7497 23.8511 18.0163 23.8828 18.2998 23.8828C18.6722 23.8828 18.9832 23.8299 19.2329 23.7241C19.4868 23.6183 19.6772 23.4702 19.8042 23.2798C19.9312 23.0894 19.9946 22.8693 19.9946 22.6196ZM24.8442 24.251L26.7104 18.1318H28.3481L25.5933 26.0474C25.5298 26.2166 25.4473 26.4007 25.3457 26.5996C25.2441 26.7985 25.1108 26.9868 24.9458 27.1646C24.785 27.3465 24.584 27.4925 24.3428 27.6025C24.1016 27.7168 23.8096 27.7739 23.4668 27.7739C23.3314 27.7739 23.2002 27.7612 23.0732 27.7358C22.9505 27.7147 22.8341 27.6914 22.7241 27.666L22.7178 26.498C22.7601 26.5023 22.8109 26.5065 22.8701 26.5107C22.9336 26.515 22.9844 26.5171 23.0225 26.5171C23.2764 26.5171 23.488 26.4854 23.6572 26.4219C23.8265 26.3626 23.964 26.2653 24.0698 26.1299C24.1799 25.9945 24.2729 25.8125 24.3491 25.584L24.8442 24.251ZM23.7905 18.1318L25.4219 23.2734L25.6948 24.8857L24.6348 25.1587L22.1401 18.1318H23.7905ZM30.7412 19.5981V25H29.2114V18.1318H30.6523L30.7412 19.5981ZM30.4683 21.312L29.9731 21.3057C29.9774 20.819 30.0451 20.3726 30.1763 19.9663C30.3117 19.5601 30.4979 19.2109 30.7349 18.9189C30.9761 18.627 31.2638 18.4027 31.5981 18.2461C31.9325 18.0853 32.3049 18.0049 32.7153 18.0049C33.0454 18.0049 33.3438 18.0514 33.6104 18.1445C33.8812 18.2334 34.1118 18.3794 34.3022 18.5825C34.4969 18.7856 34.645 19.0501 34.7466 19.376C34.8481 19.6976 34.8989 20.0933 34.8989 20.563V25H33.3628V20.5566C33.3628 20.2266 33.3141 19.9663 33.2168 19.7759C33.1237 19.5812 32.9862 19.4437 32.8042 19.3633C32.6265 19.2786 32.4043 19.2363 32.1377 19.2363C31.8753 19.2363 31.6405 19.2913 31.4331 19.4014C31.2257 19.5114 31.0501 19.6616 30.9062 19.8521C30.7666 20.0425 30.6587 20.2625 30.5825 20.5122C30.5063 20.7619 30.4683 21.0285 30.4683 21.312ZM39.2661 23.9082C39.5158 23.9082 39.7401 23.8595 39.939 23.7622C40.1421 23.6606 40.305 23.521 40.4277 23.3433C40.5547 23.1655 40.6245 22.9603 40.6372 22.7275H42.0781C42.0697 23.1719 41.9385 23.576 41.6846 23.9399C41.4307 24.3039 41.0942 24.5938 40.6753 24.8096C40.2563 25.0212 39.793 25.127 39.2852 25.127C38.7604 25.127 38.3034 25.0381 37.9141 24.8604C37.5247 24.6784 37.201 24.4287 36.9429 24.1113C36.6847 23.7939 36.4901 23.4279 36.3589 23.0132C36.2319 22.5985 36.1685 22.1541 36.1685 21.6802V21.458C36.1685 20.984 36.2319 20.5397 36.3589 20.125C36.4901 19.7061 36.6847 19.3379 36.9429 19.0205C37.201 18.7031 37.5247 18.4556 37.9141 18.2778C38.3034 18.0959 38.7583 18.0049 39.2788 18.0049C39.8289 18.0049 40.3114 18.1149 40.7261 18.335C41.1408 18.5508 41.4666 18.8534 41.7036 19.2427C41.9448 19.6278 42.0697 20.0763 42.0781 20.5884H40.6372C40.6245 20.3345 40.561 20.106 40.4468 19.9028C40.3368 19.6955 40.1802 19.5304 39.9771 19.4077C39.7782 19.285 39.5391 19.2236 39.2598 19.2236C38.9508 19.2236 38.6948 19.2871 38.4917 19.4141C38.2886 19.5368 38.1299 19.7061 38.0156 19.9219C37.9014 20.1335 37.8188 20.3726 37.7681 20.6392C37.7215 20.9015 37.6982 21.1745 37.6982 21.458V21.6802C37.6982 21.9637 37.7215 22.2388 37.7681 22.5054C37.8146 22.772 37.895 23.0111 38.0093 23.2227C38.1278 23.43 38.2886 23.5972 38.4917 23.7241C38.6948 23.8468 38.953 23.9082 39.2661 23.9082ZM52.4312 23.7368V25H47.791V23.7368H52.4312ZM48.2354 15.7578V25H46.6421V15.7578H48.2354ZM55.1479 18.1318V25H53.6118V18.1318H55.1479ZM53.5103 16.3291C53.5103 16.0964 53.5864 15.9038 53.7388 15.7515C53.8953 15.5949 54.1112 15.5166 54.3862 15.5166C54.6571 15.5166 54.8708 15.5949 55.0273 15.7515C55.1839 15.9038 55.2622 16.0964 55.2622 16.3291C55.2622 16.5576 55.1839 16.748 55.0273 16.9004C54.8708 17.0527 54.6571 17.1289 54.3862 17.1289C54.1112 17.1289 53.8953 17.0527 53.7388 16.9004C53.5864 16.748 53.5103 16.5576 53.5103 16.3291ZM56.8301 15.25H58.3599V23.5337L58.2139 25H56.8301V15.25ZM62.8413 21.5024V21.6357C62.8413 22.1436 62.7842 22.6112 62.6699 23.0386C62.5599 23.4618 62.3906 23.8299 62.1621 24.1431C61.9378 24.4562 61.6585 24.6995 61.3242 24.873C60.9941 25.0423 60.6112 25.127 60.1753 25.127C59.7479 25.127 59.3755 25.0465 59.0581 24.8857C58.7407 24.7249 58.4741 24.4964 58.2583 24.2002C58.0467 23.904 57.8753 23.5506 57.7441 23.1401C57.613 22.7297 57.5199 22.2769 57.4648 21.7817V21.3564C57.5199 20.8571 57.613 20.4043 57.7441 19.998C57.8753 19.5876 58.0467 19.2342 58.2583 18.938C58.4741 18.6375 58.7386 18.4069 59.0518 18.2461C59.3691 18.0853 59.7394 18.0049 60.1626 18.0049C60.6027 18.0049 60.9899 18.0895 61.3242 18.2588C61.6628 18.4281 61.9442 18.6693 62.1685 18.9824C62.3927 19.2913 62.5599 19.6595 62.6699 20.0869C62.7842 20.5143 62.8413 20.9862 62.8413 21.5024ZM61.3115 21.6357V21.5024C61.3115 21.1935 61.2861 20.9036 61.2354 20.6328C61.1846 20.3577 61.0999 20.1165 60.9814 19.9092C60.8672 19.7018 60.7106 19.5389 60.5117 19.4204C60.3171 19.2977 60.0737 19.2363 59.7817 19.2363C59.5109 19.2363 59.2782 19.2829 59.0835 19.376C58.8888 19.4691 58.7259 19.596 58.5947 19.7568C58.4635 19.9176 58.3599 20.1038 58.2837 20.3154C58.2118 20.527 58.1631 20.7555 58.1377 21.001V22.1499C58.1758 22.4673 58.2562 22.7593 58.3789 23.0259C58.5059 23.2882 58.6836 23.4998 58.9121 23.6606C59.1406 23.8172 59.4347 23.8955 59.7944 23.8955C60.078 23.8955 60.3171 23.8384 60.5117 23.7241C60.7064 23.6099 60.8608 23.4512 60.9751 23.248C61.0936 23.0407 61.1782 22.7995 61.229 22.5244C61.284 22.2493 61.3115 21.9531 61.3115 21.6357ZM65.6724 19.4395V25H64.1426V18.1318H65.6025L65.6724 19.4395ZM67.7734 18.0874L67.7607 19.5093C67.6676 19.4924 67.5661 19.4797 67.4561 19.4712C67.3503 19.4627 67.2445 19.4585 67.1387 19.4585C66.8763 19.4585 66.6457 19.4966 66.4468 19.5728C66.2479 19.6447 66.0807 19.7505 65.9453 19.8901C65.8141 20.0256 65.7126 20.1906 65.6406 20.3853C65.5687 20.5799 65.5264 20.7979 65.5137 21.0391L65.1646 21.0645C65.1646 20.6328 65.2069 20.2329 65.2915 19.8647C65.3761 19.4966 65.5031 19.1729 65.6724 18.8936C65.8459 18.6143 66.0617 18.3963 66.3198 18.2397C66.5822 18.0832 66.8848 18.0049 67.2275 18.0049C67.3206 18.0049 67.4201 18.0133 67.5259 18.0303C67.6359 18.0472 67.7184 18.0662 67.7734 18.0874ZM72.3755 23.6226V20.3472C72.3755 20.1017 72.3311 19.8901 72.2422 19.7124C72.1533 19.5347 72.0179 19.3971 71.8359 19.2998C71.6582 19.2025 71.4339 19.1538 71.1631 19.1538C70.9134 19.1538 70.6976 19.1961 70.5156 19.2808C70.3337 19.3654 70.1919 19.4797 70.0903 19.6235C69.9888 19.7674 69.938 19.9303 69.938 20.1123H68.4146C68.4146 19.8415 68.4801 19.5791 68.6113 19.3252C68.7425 19.0713 68.9329 18.8449 69.1826 18.646C69.4323 18.4471 69.7306 18.2905 70.0776 18.1763C70.4246 18.062 70.814 18.0049 71.2456 18.0049C71.7619 18.0049 72.2189 18.0916 72.6167 18.2651C73.0187 18.4386 73.334 18.701 73.5625 19.0522C73.7952 19.3993 73.9116 19.8351 73.9116 20.3599V23.4131C73.9116 23.7262 73.9328 24.0076 73.9751 24.2573C74.0216 24.5028 74.0872 24.7165 74.1719 24.8984V25H72.604C72.5321 24.835 72.4749 24.6255 72.4326 24.3716C72.3945 24.1134 72.3755 23.8638 72.3755 23.6226ZM72.5977 20.8232L72.6104 21.769H71.5122C71.2287 21.769 70.979 21.7965 70.7632 21.8516C70.5474 21.9023 70.3675 21.9785 70.2236 22.0801C70.0798 22.1816 69.9718 22.3044 69.8999 22.4482C69.828 22.5921 69.792 22.755 69.792 22.937C69.792 23.119 69.8343 23.2861 69.9189 23.4385C70.0036 23.5866 70.1263 23.703 70.2871 23.7876C70.4521 23.8722 70.651 23.9146 70.8838 23.9146C71.1969 23.9146 71.4699 23.8511 71.7026 23.7241C71.9396 23.5929 72.1258 23.4342 72.2612 23.248C72.3966 23.0576 72.4686 22.8778 72.4771 22.7085L72.9722 23.3877C72.9214 23.5612 72.8346 23.7474 72.7119 23.9463C72.5892 24.1452 72.4284 24.3356 72.2295 24.5176C72.0348 24.6953 71.8 24.8413 71.5249 24.9556C71.2541 25.0698 70.9409 25.127 70.5854 25.127C70.1369 25.127 69.737 25.0381 69.3857 24.8604C69.0345 24.6784 68.7594 24.4351 68.5605 24.1304C68.3617 23.8215 68.2622 23.4723 68.2622 23.083C68.2622 22.7191 68.3299 22.3975 68.4653 22.1182C68.605 21.8346 68.8081 21.5977 69.0747 21.4072C69.3455 21.2168 69.6756 21.0729 70.0649 20.9756C70.4543 20.874 70.8986 20.8232 71.3979 20.8232H72.5977ZM77.0474 19.4395V25H75.5176V18.1318H76.9775L77.0474 19.4395ZM79.1484 18.0874L79.1357 19.5093C79.0426 19.4924 78.9411 19.4797 78.8311 19.4712C78.7253 19.4627 78.6195 19.4585 78.5137 19.4585C78.2513 19.4585 78.0207 19.4966 77.8218 19.5728C77.6229 19.6447 77.4557 19.7505 77.3203 19.8901C77.1891 20.0256 77.0876 20.1906 77.0156 20.3853C76.9437 20.5799 76.9014 20.7979 76.8887 21.0391L76.5396 21.0645C76.5396 20.6328 76.5819 20.2329 76.6665 19.8647C76.7511 19.4966 76.8781 19.1729 77.0474 18.8936C77.2209 18.6143 77.4367 18.3963 77.6948 18.2397C77.9572 18.0832 78.2598 18.0049 78.6025 18.0049C78.6956 18.0049 78.7951 18.0133 78.9009 18.0303C79.0109 18.0472 79.0934 18.0662 79.1484 18.0874ZM82.2144 24.251L84.0806 18.1318H85.7183L82.9634 26.0474C82.8999 26.2166 82.8174 26.4007 82.7158 26.5996C82.6143 26.7985 82.481 26.9868 82.3159 27.1646C82.1551 27.3465 81.9541 27.4925 81.7129 27.6025C81.4717 27.7168 81.1797 27.7739 80.8369 27.7739C80.7015 27.7739 80.5703 27.7612 80.4434 27.7358C80.3206 27.7147 80.2043 27.6914 80.0942 27.666L80.0879 26.498C80.1302 26.5023 80.181 26.5065 80.2402 26.5107C80.3037 26.515 80.3545 26.5171 80.3926 26.5171C80.6465 26.5171 80.8581 26.4854 81.0273 26.4219C81.1966 26.3626 81.3341 26.2653 81.4399 26.1299C81.55 25.9945 81.6431 25.8125 81.7192 25.584L82.2144 24.251ZM81.1606 18.1318L82.792 23.2734L83.0649 24.8857L82.0049 25.1587L79.5103 18.1318H81.1606Z",fill:"white"}),(0,e.createElement)("defs",null,(0,e.createElement)("filter",{id:"filter0_d_1707_1883",x:"0",y:"0",width:"100",height:"44",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},(0,e.createElement)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,e.createElement)("feColorMatrix",{"in":"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),(0,e.createElement)("feOffset",{dy:"4"}),(0,e.createElement)("feGaussianBlur",{stdDeviation:"4"}),(0,e.createElement)("feComposite",{in2:"hardAlpha",operator:"out"}),(0,e.createElement)("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.0666667 0 0 0 0 0.0901961 0 0 0 0 0.133333 0 0 0 0.3 0"}),(0,e.createElement)("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_1707_1883"}),(0,e.createElement)("feBlend",{mode:"normal","in":"SourceGraphic",in2:"effect1_dropShadow_1707_1883",result:"shape"}))))}function u(){return(0,e.createElement)("svg",{className:"close",xmlns:"http://www.w3.org/2000/svg",width:"64",height:"44",viewBox:"0 0 64 44",fill:"none"},(0,e.createElement)("g",{filter:"url(#filter0_d_1707_1913)"},(0,e.createElement)("path",{d:"M56 29C56 30.6569 54.6569 32 53 32H11C9.34315 32 8 30.6569 8 29V13C8 11.3431 9.34315 10 11 10H24.293C25.3468 10 26.3579 9.58421 27.1068 8.84297L32 4L36.8932 8.84297C37.6421 9.58421 38.6532 10 39.707 10H53C54.6569 10 56 11.3431 56 13V29Z",fill:"#111722"})),(0,e.createElement)("path",{d:"M22.4771 21.9912H24.064C24.0132 22.5964 23.8439 23.1359 23.5562 23.6099C23.2684 24.0796 22.8643 24.4499 22.3438 24.7207C21.8232 24.9915 21.1906 25.127 20.4458 25.127C19.8745 25.127 19.3604 25.0254 18.9033 24.8223C18.4463 24.6149 18.0549 24.3229 17.729 23.9463C17.4032 23.5654 17.1535 23.1063 16.98 22.5688C16.8107 22.0314 16.7261 21.4305 16.7261 20.7661V19.998C16.7261 19.3337 16.8128 18.7327 16.9863 18.1953C17.1641 17.6579 17.418 17.1987 17.748 16.8179C18.0781 16.4328 18.4738 16.1387 18.9351 15.9355C19.4006 15.7324 19.9232 15.6309 20.5029 15.6309C21.2393 15.6309 21.8613 15.7663 22.3691 16.0371C22.877 16.3079 23.2705 16.6825 23.5498 17.1606C23.8333 17.6388 24.0068 18.1868 24.0703 18.8047H22.4834C22.4411 18.4069 22.348 18.0662 22.2041 17.7827C22.0645 17.4992 21.8571 17.2834 21.582 17.1353C21.307 16.9829 20.9473 16.9067 20.5029 16.9067C20.139 16.9067 19.8216 16.9744 19.5508 17.1099C19.2799 17.2453 19.0535 17.4442 18.8716 17.7065C18.6896 17.9689 18.5521 18.2926 18.459 18.6777C18.3701 19.0586 18.3257 19.4945 18.3257 19.9854V20.7661C18.3257 21.2316 18.3659 21.6548 18.4463 22.0356C18.5309 22.4123 18.6579 22.736 18.8271 23.0068C19.0007 23.2777 19.2207 23.4871 19.4873 23.6353C19.7539 23.7834 20.0734 23.8574 20.4458 23.8574C20.8986 23.8574 21.2646 23.7855 21.5439 23.6416C21.8275 23.4977 22.0412 23.2882 22.1851 23.0132C22.3332 22.7339 22.4305 22.3932 22.4771 21.9912ZM26.9966 15.25V25H25.4604V15.25H26.9966ZM28.3804 21.6421V21.4961C28.3804 21.001 28.4523 20.5418 28.5962 20.1187C28.7401 19.6912 28.9474 19.321 29.2183 19.0078C29.4933 18.6904 29.8276 18.445 30.2212 18.2715C30.619 18.0938 31.0675 18.0049 31.5669 18.0049C32.0705 18.0049 32.519 18.0938 32.9126 18.2715C33.3104 18.445 33.6468 18.6904 33.9219 19.0078C34.1969 19.321 34.4064 19.6912 34.5503 20.1187C34.6942 20.5418 34.7661 21.001 34.7661 21.4961V21.6421C34.7661 22.1372 34.6942 22.5964 34.5503 23.0195C34.4064 23.4427 34.1969 23.813 33.9219 24.1304C33.6468 24.4435 33.3125 24.689 32.9189 24.8667C32.5254 25.0402 32.0789 25.127 31.5796 25.127C31.076 25.127 30.6253 25.0402 30.2275 24.8667C29.834 24.689 29.4997 24.4435 29.2246 24.1304C28.9495 23.813 28.7401 23.4427 28.5962 23.0195C28.4523 22.5964 28.3804 22.1372 28.3804 21.6421ZM29.9102 21.4961V21.6421C29.9102 21.951 29.9419 22.243 30.0054 22.5181C30.0688 22.7931 30.1683 23.0343 30.3037 23.2417C30.4391 23.4491 30.6126 23.612 30.8242 23.7305C31.0358 23.849 31.2876 23.9082 31.5796 23.9082C31.8631 23.9082 32.1086 23.849 32.3159 23.7305C32.5275 23.612 32.701 23.4491 32.8364 23.2417C32.9718 23.0343 33.0713 22.7931 33.1348 22.5181C33.2025 22.243 33.2363 21.951 33.2363 21.6421V21.4961C33.2363 21.1914 33.2025 20.9036 33.1348 20.6328C33.0713 20.3577 32.9697 20.1144 32.8301 19.9028C32.6947 19.6912 32.5212 19.5262 32.3096 19.4077C32.1022 19.285 31.8547 19.2236 31.5669 19.2236C31.2791 19.2236 31.0295 19.285 30.8179 19.4077C30.6105 19.5262 30.4391 19.6912 30.3037 19.9028C30.1683 20.1144 30.0688 20.3577 30.0054 20.6328C29.9419 20.9036 29.9102 21.1914 29.9102 21.4961ZM39.9014 23.1401C39.9014 22.9878 39.8633 22.8503 39.7871 22.7275C39.7109 22.6006 39.5649 22.4863 39.3491 22.3848C39.1375 22.2832 38.8244 22.1901 38.4097 22.1055C38.0457 22.0251 37.7114 21.9299 37.4067 21.8198C37.1063 21.7056 36.8481 21.568 36.6323 21.4072C36.4165 21.2464 36.2493 21.056 36.1309 20.8359C36.0124 20.6159 35.9531 20.362 35.9531 20.0742C35.9531 19.7949 36.0145 19.5304 36.1372 19.2808C36.2599 19.0311 36.4355 18.811 36.6641 18.6206C36.8926 18.4302 37.1698 18.2799 37.4956 18.1699C37.8257 18.0599 38.1938 18.0049 38.6001 18.0049C39.1756 18.0049 39.6686 18.1022 40.0791 18.2969C40.4938 18.4873 40.8112 18.7476 41.0312 19.0776C41.2513 19.4035 41.3613 19.7716 41.3613 20.1821H39.8315C39.8315 20.0002 39.785 19.8309 39.6919 19.6743C39.603 19.5135 39.4676 19.3844 39.2856 19.2871C39.1037 19.1855 38.8752 19.1348 38.6001 19.1348C38.3377 19.1348 38.1198 19.1771 37.9463 19.2617C37.777 19.3421 37.6501 19.4479 37.5654 19.5791C37.485 19.7103 37.4448 19.8542 37.4448 20.0107C37.4448 20.125 37.466 20.2287 37.5083 20.3218C37.5549 20.4106 37.631 20.4932 37.7368 20.5693C37.8426 20.6413 37.9865 20.709 38.1685 20.7725C38.3547 20.8359 38.5874 20.8973 38.8667 20.9565C39.3914 21.0666 39.8421 21.2083 40.2188 21.3818C40.5996 21.5511 40.8916 21.7712 41.0947 22.042C41.2979 22.3086 41.3994 22.6471 41.3994 23.0576C41.3994 23.3623 41.3338 23.6416 41.2026 23.8955C41.0757 24.1452 40.8895 24.3631 40.644 24.5493C40.3986 24.7313 40.1045 24.873 39.7617 24.9746C39.4232 25.0762 39.0423 25.127 38.6191 25.127C37.9971 25.127 37.4702 25.0169 37.0386 24.7969C36.6069 24.5726 36.279 24.2869 36.0547 23.9399C35.8346 23.5887 35.7246 23.2248 35.7246 22.8481H37.2036C37.2205 23.1317 37.2988 23.3581 37.4385 23.5273C37.5824 23.6924 37.7601 23.813 37.9717 23.8892C38.1875 23.9611 38.4097 23.9971 38.6382 23.9971C38.9132 23.9971 39.1439 23.9611 39.3301 23.8892C39.5163 23.813 39.658 23.7114 39.7554 23.5845C39.8527 23.4533 39.9014 23.3052 39.9014 23.1401ZM45.7666 25.127C45.2588 25.127 44.7996 25.0444 44.3892 24.8794C43.9829 24.7101 43.6359 24.4753 43.3481 24.1748C43.0646 23.8743 42.8467 23.521 42.6943 23.1147C42.542 22.7085 42.4658 22.2705 42.4658 21.8008V21.5469C42.4658 21.0094 42.5441 20.5228 42.7007 20.0869C42.8573 19.651 43.0752 19.2786 43.3545 18.9697C43.6338 18.6566 43.9639 18.4175 44.3447 18.2524C44.7256 18.0874 45.1382 18.0049 45.5825 18.0049C46.0734 18.0049 46.5029 18.0874 46.8711 18.2524C47.2393 18.4175 47.5439 18.6502 47.7852 18.9507C48.0306 19.2469 48.2126 19.6003 48.3311 20.0107C48.4538 20.4212 48.5151 20.874 48.5151 21.3691V22.0229H43.2085V20.9248H47.0044V20.8042C46.9959 20.5291 46.9409 20.271 46.8394 20.0298C46.742 19.7886 46.5918 19.5939 46.3887 19.4458C46.1855 19.2977 45.9147 19.2236 45.5762 19.2236C45.3223 19.2236 45.0959 19.2786 44.897 19.3887C44.7023 19.4945 44.5394 19.6489 44.4082 19.8521C44.277 20.0552 44.1755 20.3006 44.1035 20.5884C44.0358 20.8719 44.002 21.1914 44.002 21.5469V21.8008C44.002 22.1012 44.0422 22.3805 44.1226 22.6387C44.2072 22.8926 44.3299 23.1147 44.4907 23.3052C44.6515 23.4956 44.8462 23.6458 45.0747 23.7559C45.3032 23.8617 45.5635 23.9146 45.8555 23.9146C46.2236 23.9146 46.5516 23.8405 46.8394 23.6924C47.1271 23.5443 47.3768 23.3348 47.5884 23.064L48.3945 23.8447C48.2464 24.0605 48.0539 24.2679 47.8169 24.4668C47.5799 24.6615 47.29 24.8201 46.9473 24.9429C46.6087 25.0656 46.2152 25.127 45.7666 25.127Z",fill:"white"}),(0,e.createElement)("defs",null,(0,e.createElement)("filter",{id:"filter0_d_1707_1913",x:"0",y:"0",width:"64",height:"44",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},(0,e.createElement)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,e.createElement)("feColorMatrix",{"in":"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),(0,e.createElement)("feOffset",{dy:"4"}),(0,e.createElement)("feGaussianBlur",{stdDeviation:"4"}),(0,e.createElement)("feComposite",{in2:"hardAlpha",operator:"out"}),(0,e.createElement)("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.0666667 0 0 0 0 0.0901961 0 0 0 0 0.133333 0 0 0 0.3 0"}),(0,e.createElement)("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_1707_1913"}),(0,e.createElement)("feBlend",{mode:"normal","in":"SourceGraphic",in2:"effect1_dropShadow_1707_1913",result:"shape"}))))}const g=()=>{const{loadLibrary:a,templateType:n,dispatch:l,syncLibrary:i,filter:g,showSinglePage:d}=s();(0,t.useEffect)((()=>{document.addEventListener("keydown",(function(e){"Escape"!==e.key&&"Esc"!==e.key||h()}))}),[]);const E=(0,t.useRef)(null);(0,t.useEffect)((()=>{if(E.current){let e=Array.from(E.current.querySelectorAll(".gutenkit-library-menu-item")),t=e.findIndex((e=>e.classList.contains("is-active")));if(-1!==t){let a=e[t].clientWidth+2;E.current.style.setProperty("--width",`${a}px`);let r=e.slice(0,t).reduce(((e,t)=>e+t.clientWidth+20),0);E.current.style.setProperty("--translate",`${r}px`)}}}),[n]);const h=()=>{l({type:"SET_LOAD_LIBRARY",loadLibrary:!a}),l({type:"SET_IS_SINGLE_PAGE",isSinglePage:!1}),l({type:"SET_TEMPLATE_TYPE",templateType:"patterns"})},y=((e,a)=>{const r=(0,t.useRef)(null),n=(0,t.useRef)(null);return(0,t.useEffect)((()=>()=>{clearTimeout(r.current)}),[]),e=>{clearTimeout(r.current),e!==n.current&&(r.current=setTimeout((()=>{(e=>{l({type:"SET_SEARCH_INPUT",searchInput:e})})(e),n.current=e}),500))}})(),f=d?{opacity:"0",visibility:"hidden",cursor:"none"}:{opacity:"1",visibility:"visible",cursor:"pointer"};return(0,e.createElement)("div",{className:"interface-interface-skeleton__header gutenkit-library-header"},(0,e.createElement)("div",{className:"edit-post-header edit-site-header-edit-mode gutenkit-library-header-content"},(0,e.createElement)("div",{className:"gutenkit-library-logo"},(0,e.createElement)(o,null)),(0,e.createElement)("div",{className:"gutenkit-library-menu",ref:E},[{name:"Patterns",slug:"patterns"},{name:"Templates",slug:"templates"},{name:"Pages",slug:"pages"}].map(((t,a)=>(0,e.createElement)(r.Button,{key:a,variant:"link",className:"gutenkit-library-menu-item "+(n===t.slug?"is-active":""),onClick:()=>(e=>{l({type:"SET_TEMPLATE_TYPE",templateType:e.slug}),l({type:"SET_FILTER",filter:{category:"all"}}),l({type:"SET_SEARCH_INPUT",searchInput:""}),l({type:"SET_IS_SINGLE_PAGE",isSinglePage:!1}),l({type:"SET_KEY_WORDS",keyWords:""})})(t)},t.name))),(0,e.createElement)("span",{className:"under-line"})),(0,e.createElement)("div",{className:"gutenkit-library-search"},(0,e.createElement)("div",{style:f},(0,e.createElement)(p,{onChange:e=>{y(e)},onClick:e=>e.target.focus(),onClose:()=>{""!==g.category&&"patterns"===n&&(l({type:"SET_FILTER",filter:{category:"all"}}),l({type:"SET_SEARCH_INPUT",searchInput:""}),l({type:"SET_PATTERNS",patterns:[]})),"pages"===n&&(l({type:"SET_SEARCH_INPUT",searchInput:""}),l({type:"SET_PAGES",pages:[]}),l({type:"SET_PAGE_HAS_MORE",pageHasMore:!0})),"templates"===n&&(l({type:"SET_SEARCH_INPUT",searchInput:""}),l({type:"SET_TEMPLATES",templates:[]}),l({type:"SET_TEMPLATES_HAS_MORE",templatesHasMore:!0}))},className:"gutenkit-library-search-input",placeholder:`Search ${n}...`})),(0,e.createElement)("div",{className:"gutenkit-library-icon",style:f},(0,e.createElement)(r.Button,{variant:"tertiary",icon:(0,e.createElement)(c,null),className:"gutenkit-library-synchronize "+(i?"is-active":""),onClick:()=>l({type:"SET_SYNC_LIBRARY",syncLibrary:!0})}),(0,e.createElement)(m,null)),(0,e.createElement)("span",{className:"gutenkit-library-separate",style:f}),(0,e.createElement)("div",{className:"gutenkit-library-icon"},(0,e.createElement)(r.Button,{onClick:h,variant:"tertiary",icon:(0,e.createElement)(C,null),className:"gutenkit-template-library__close"}),(0,e.createElement)(u,null)))))},d=window.wp.data;function E(){return(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"none"},(0,e.createElement)("path",{d:"M9 17C13.4182 17 17 13.4182 17 9C17 4.58172 13.4182 1 9 1C4.58172 1 1 4.58172 1 9C1 13.4182 4.58172 17 9 17Z",stroke:"#111722",strokeWidth:"1.5"}),(0,e.createElement)("path",{d:"M9 5V9.4M7.4 8.2L8.43432 9.23432C8.70096 9.50096 8.83432 9.63432 9 9.63432C9.16568 9.63432 9.29904 9.50096 9.56568 9.23432L10.6 8.2",stroke:"#111722",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,e.createElement)("path",{d:"M6.59219 12.2H11.3922",stroke:"#111722",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))}function h(){return(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"},(0,e.createElement)("circle",{cx:"12",cy:"12",r:"12",fill:"white"}),(0,e.createElement)("path",{d:"M9 15L15 9",stroke:"#111722",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,e.createElement)("path",{d:"M9 9H15V15",stroke:"#111722",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))}function y(){return(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"27",height:"26",viewBox:"0 0 27 26",fill:"none"},(0,e.createElement)("circle",{cx:"13.5",cy:"13",r:"13",fill:"white"}),(0,e.createElement)("path",{d:"M6.5 13.0909C6.5 13.0909 9.04545 8 13.5 8C17.9545 8 20.5 13.0909 20.5 13.0909C20.5 13.0909 17.9545 18.1818 13.5 18.1818C9.04545 18.1818 6.5 13.0909 6.5 13.0909Z",stroke:"#111722",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,e.createElement)("path",{d:"M13.4999 14.9999C14.5543 14.9999 15.409 14.1452 15.409 13.0909C15.409 12.0365 14.5543 11.1818 13.4999 11.1818C12.4455 11.1818 11.5908 12.0365 11.5908 13.0909C11.5908 14.1452 12.4455 14.9999 13.4999 14.9999Z",stroke:"#111722",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))}const f=({href:t,icon:a=!0,children:r,...n})=>{const l=n.rel?`${n.rel} noreferrer noopener`:"noreferrer noopener";return(0,e.createElement)("a",{...n,className:"gutenkit-external-link",href:t,target:"_blank",rel:l},r,a?(0,e.createElement)(h,null):(0,e.createElement)(y,null))},_=window.wp.i18n,k=a.p+"images/placeholder.9201fa54.png",w=({src:a,alt:r})=>{const n=new URL(k,window.location.origin).href,[l,i]=(0,t.useState)(n),[s,o]=(0,t.useState)(!1),c=(0,t.useRef)(null);return(0,t.useEffect)((()=>{let e,t;if("undefined"!=typeof window&&"IntersectionObserver"in window&&(e=new IntersectionObserver((a=>{a.forEach((a=>{a.isIntersecting&&(o(!0),e.unobserve(t))}))})),t=c.current,t&&e.observe(t)),s){const e=new Image;e.src=a,e.onload=()=>{i(a)}}return()=>{e&&t&&e.unobserve(t)}}),[a,s]),(0,e.createElement)("img",{className:"lazy-image",src:l,alt:r,ref:c})},L=window.wp.apiFetch;var S=a.n(L);const b=(e,a,r)=>{const[n,l]=(0,t.useState)("Install and Import"),[i,s]=(0,t.useState)(null),o=(0,t.useRef)(null),c=(0,t.useCallback)((()=>{S()({path:"/wp/v2/plugins/gutenkit-blocks-addon/gutenkit-blocks-addon"}).then((e=>{s(e),l("active"===e.status?"Install and Import":"Activate and Import")}))["catch"]((e=>{console.warn("Activated failed: ",e.message)}))}),[]),C=(0,t.useCallback)((()=>{l("Activating"),S()({path:"/wp/v2/plugins/gutenkit-blocks-addon/gutenkit-blocks-addon",method:"POST",data:{...i,status:"active"}}).then((e=>{s(e),l("Activated")}))["catch"]((e=>{console.warn("Activated failed: ",e.message)}))}),[i]),p=(0,t.useCallback)((()=>{l("Installing"),S()({path:"/wp/v2/plugins?slug=gutenkit-blocks-addon&status=active",method:"POST",parse:!1}).then((e=>{s(e),l("Activated")}))["catch"]((e=>{console.warn("Install failed: ",e.message)}))}),[]),m=(0,t.useCallback)((()=>{"Install and Import"===n?p():"Activate and Import"===n&&C()}),[n,C,p]);return(0,t.useEffect)((()=>{c()}),[c]),(0,t.useEffect)((()=>{(async()=>{if("free"===e?.["package"]&&"Activated"===n&&o.current){r(!0);try{await a(e),r(!1),(0,d.dispatch)("core/editor").savePost(),setTimeout((()=>{window.location.reload()}),3e3)}catch(e){console.warn("import failed: ",e.message),r(!1)}}})()}),[e,n,a,d.dispatch]),{status:n,handlePluginInstall:m,importButtonRef:o}},T=function({page:a,handlePageImport:n}){const[i,s]=(0,t.useState)(!1),{status:o,handlePluginInstall:c,importButtonRef:C}=b(a,n,s);(0,t.useEffect)((()=>{i&&document.querySelectorAll(".gutenkit-library-list-item-inner-content-thumbnail:not(.is-loading)").forEach((e=>{e.classList.add("disabled")}))}),[i]);const p=l()("gutenkit-library-list-item-inner-content-thumbnail",{"is-loading":i}),m=l()("gutenkit-library-list-item",{"pro-inactive":"pro"===a?.["package"]&&!0}),u=l()("gutenkit-library-list-item__title",{"is-premium":"pro"===a?.["package"]});return(0,e.createElement)("li",{className:m,key:a?.ID},(0,e.createElement)("div",{className:p},(0,e.createElement)(w,{src:a?.thumbnail,alt:a?.title}),(0,e.createElement)("div",{className:"gutenkit-library-list-item-inner-content-overlay"},"pro"===a?.["package"]&&!1,"pro"===a?.["package"]&&(0,e.createElement)(f,{href:"https://wpmet.com/plugin/gutenkit/"},(0,_.__)("Requires GutenKit Blocks PRO","gutenkit-blocks-addon")),"free"===a?.["package"]&&"Activated"===o&&(0,e.createElement)(r.Button,{ref:C,onClick:async()=>{s(!0),await n(a),s(!1)},className:"gutenkit-import-button",icon:i?(0,e.createElement)(r.Spinner,{className:"importing-spinner"}):(0,e.createElement)(E,null),disabled:!!i},i?(0,_.__)("Importing","gutenkit-blocks-addon"):(0,_.__)("Import","gutenkit-blocks-addon")),"free"===a?.["package"]&&"Activated"!==o&&(0,e.createElement)(r.Button,{onClick:c,className:"gutenkit-import-button",icon:"Installing"===o||"Activating"===o?(0,e.createElement)(r.Spinner,{className:"importing-spinner"}):(0,e.createElement)(E,null),disabled:!!i},(0,_.__)(`${o}`,"gutenkit-blocks-addon")))),(0,e.createElement)("div",{className:u},(0,e.createElement)("span",{className:"item-title"},a?.title)))},v=window.wp.blocks,M=window.wp.primitives,P=()=>(0,e.createElement)("div",{className:"gutenkit-library-empty",style:{textAlign:"center",color:"#888",padding:"20px"}},(0,e.createElement)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",width:"300",height:"212",viewBox:"0 0 300 212",fill:"none"},(0,e.createElement)(M.Path,{d:"M274.542 200.396C271.038 198.174 267.763 195.725 264.778 193.13C259.616 188.645 259.056 184.493 259.513 181.795C260.031 178.722 262.187 176.003 265.027 174.861C268.758 173.345 271.142 171.954 272.096 170.709C272.614 170.045 272.676 169.484 272.655 167.886C272.635 165.498 268.551 161.616 257.191 157.941C248.629 155.18 239.902 153.79 239.798 153.79L240.856 146.918C241.229 146.98 250.143 148.371 259.243 151.298C272.863 155.679 279.517 161.097 279.579 167.802C279.641 173.511 277.796 177.144 267.597 181.276C267.183 181.442 266.478 182.044 266.333 182.916C266.125 184.224 267.203 186.03 269.318 187.857C277.382 194.873 287.83 200.624 298.692 204.049L296.598 210.672C288.866 208.264 281.3 204.714 274.542 200.396Z",fill:"white"}),(0,e.createElement)(M.Path,{d:"M297.285 212L296.29 211.688C288.537 209.239 280.825 205.647 273.984 201.267C270.419 199.004 267.102 196.513 264.096 193.918C258.561 189.102 257.981 184.576 258.478 181.628C259.038 178.223 261.464 175.172 264.635 173.905C269.071 172.099 270.688 170.833 271.269 170.086C271.538 169.733 271.642 169.484 271.621 167.906C271.6 166.349 268.491 162.695 256.882 158.958C248.507 156.259 240.008 154.889 239.656 154.848L238.619 154.682L240.008 145.755L241.045 145.921C241.418 145.983 250.394 147.395 259.577 150.343C273.673 154.889 280.577 160.598 280.639 167.823C280.701 173.864 278.711 177.954 268.014 182.292C267.89 182.334 267.454 182.666 267.372 183.144C267.268 183.829 267.869 185.282 270.004 187.129C277.965 194.043 288.267 199.731 299.005 203.115L300 203.426L297.285 212ZM275.104 199.502C281.509 203.592 288.703 206.997 295.937 209.384L297.389 204.734C286.754 201.225 276.576 195.537 268.636 188.645C266.211 186.548 265.029 184.451 265.319 182.749C265.527 181.524 266.459 180.632 267.226 180.32C276.928 176.397 278.607 173.158 278.545 167.823C278.483 161.615 272.057 156.529 258.914 152.294C251.555 149.928 244.341 148.557 241.729 148.122L240.982 152.938C243.553 153.395 250.56 154.723 257.504 156.965C267.745 160.266 273.653 164.252 273.694 167.885C273.715 169.567 273.632 170.418 272.906 171.352C271.828 172.764 269.362 174.217 265.402 175.836C262.894 176.853 260.987 179.261 260.531 181.981C260.137 184.41 260.655 188.167 265.464 192.361C268.387 194.873 271.621 197.281 275.104 199.502Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M226.158 111.854L208.765 105.854L188.201 165.663L205.594 171.663C222.095 177.351 240.067 168.57 245.747 152.066C251.407 135.541 242.659 117.542 226.158 111.854Z",fill:"#77E0B5"}),(0,e.createElement)(M.Path,{d:"M215.856 174.425C212.332 174.425 208.766 173.844 205.263 172.64L186.896 166.308L208.123 104.527L226.49 110.858C243.489 116.733 252.568 135.355 246.702 152.378C242.079 165.914 229.392 174.425 215.856 174.425ZM189.529 165.021L205.926 170.689C209.201 171.83 212.56 172.349 215.856 172.349C228.522 172.349 240.4 164.378 244.753 151.714C250.247 135.77 241.747 118.332 225.827 112.831L209.429 107.163L189.529 165.021Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M208.6 138.905C202.194 157.506 189.072 169.837 179.267 166.453C175.95 165.312 167.555 163.049 165.917 159.125C162.745 151.465 168.84 138.967 173.069 126.657C177.07 115.031 178.562 101.454 185.362 97.0322C189.446 94.3749 198.733 97.8418 202.422 99.1082C212.228 102.492 215.006 120.304 208.6 138.905Z",fill:"#F582AE"}),(0,e.createElement)(M.Path,{d:"M182.667 168.052C181.382 168.052 180.118 167.844 178.915 167.429C178.521 167.284 178.045 167.139 177.526 166.972C173.422 165.644 166.56 163.402 164.943 159.52C162.228 152.96 165.752 143.472 169.504 133.445C170.374 131.12 171.266 128.691 172.095 126.325C173.214 123.107 174.127 119.681 175.018 116.401C177.36 107.744 179.579 99.5857 184.803 96.1811C189.094 93.3785 197.925 96.451 202.153 97.9249L202.755 98.1325C207.875 99.8971 211.378 105.232 212.622 113.142C213.824 120.823 212.746 130.103 209.596 139.237C206.445 148.392 201.594 156.364 195.934 161.679C191.457 165.872 186.876 168.052 182.667 168.052ZM189.487 97.0945C188.078 97.0945 186.834 97.3229 185.943 97.9042C181.382 100.873 179.268 108.658 177.029 116.92C176.137 120.242 175.205 123.688 174.064 126.989C173.235 129.397 172.344 131.805 171.452 134.151C167.866 143.784 164.466 152.897 166.871 158.71C168.115 161.72 174.645 163.838 178.169 164.98C178.708 165.146 179.185 165.312 179.599 165.457C183.953 166.952 189.259 165.083 194.504 160.163C199.915 155.077 204.579 147.417 207.626 138.573C210.673 129.729 211.71 120.823 210.57 113.474C209.471 106.353 206.445 101.62 202.091 100.105L201.469 99.8971C198.795 98.9422 193.468 97.0945 189.487 97.0945Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M199.346 135.713C205.741 117.121 202.975 99.3071 193.167 95.9239C183.36 92.5406 170.225 104.869 163.83 123.461C157.435 142.052 160.201 159.866 170.009 163.25C179.816 166.633 192.951 154.304 199.346 135.713Z",fill:"white"}),(0,e.createElement)(M.Path,{d:"M173.401 164.855C172.115 164.855 170.851 164.648 169.649 164.232C164.528 162.468 161.025 157.132 159.781 149.223C158.579 141.542 159.657 132.262 162.808 123.128C165.959 113.972 170.809 106.001 176.469 100.686C182.315 95.2055 188.347 93.171 193.467 94.9356C198.588 96.7002 202.091 102.036 203.335 109.945C204.537 117.626 203.459 126.906 200.308 136.04C197.157 145.195 192.306 153.167 186.647 158.482C182.19 162.675 177.609 164.855 173.401 164.855ZM189.757 96.3888C186.087 96.3888 181.983 98.3818 177.92 102.202C172.489 107.288 167.845 114.948 164.798 123.792C161.75 132.636 160.714 141.542 161.854 148.891C162.953 156.011 165.979 160.745 170.333 162.26C174.686 163.755 179.993 161.886 185.237 156.966C190.669 151.88 195.312 144.22 198.36 135.376C201.407 126.532 202.443 117.626 201.303 110.277C200.204 103.157 197.178 98.4233 192.825 96.9078C191.83 96.5549 190.814 96.3888 189.757 96.3888Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M189.531 114.294C190.624 111.117 189.895 107.986 187.903 107.298C185.912 106.611 183.411 108.629 182.319 111.805C181.226 114.982 181.955 118.114 183.947 118.801C185.939 119.488 188.439 117.47 189.531 114.294Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M178.472 146.497C179.564 143.321 178.835 140.189 176.844 139.502C174.852 138.814 172.352 140.832 171.259 144.009C170.166 147.185 170.895 150.317 172.887 151.004C174.879 151.691 177.379 149.673 178.472 146.497Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M106.318 105.088L138.512 116.34C141.186 117.274 142.596 120.201 141.663 122.858C140.73 125.536 137.807 126.948 135.154 126.014L102.96 114.762L106.318 105.088Z",fill:"white"}),(0,e.createElement)(M.Path,{d:"M136.812 127.322C136.149 127.322 135.464 127.218 134.78 126.969L101.592 115.385L105.655 103.738L138.843 115.322C140.398 115.862 141.642 116.983 142.368 118.457C143.093 119.931 143.176 121.613 142.637 123.17C141.766 125.723 139.362 127.322 136.812 127.322ZM104.266 114.118L135.485 125.017C137.6 125.765 139.942 124.623 140.688 122.505C141.041 121.467 140.979 120.367 140.502 119.391C140.025 118.416 139.196 117.668 138.18 117.315L106.961 106.396L104.266 114.118Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M117.513 72.9095L149.707 84.1614C152.381 85.0956 153.79 88.0228 152.858 90.6801C151.925 93.3581 149.002 94.7698 146.348 93.8356L114.155 82.5837L117.513 72.9095Z",fill:"white"}),(0,e.createElement)(M.Path,{d:"M148.028 95.1634C147.364 95.1634 146.68 95.0596 145.996 94.8105L112.808 83.2472L116.871 71.6008L150.059 83.1849C151.614 83.7246 152.858 84.8457 153.583 86.3196C154.309 87.7936 154.392 89.4752 153.853 91.0321C152.961 93.5649 150.557 95.1634 148.028 95.1634ZM115.461 81.96L146.68 92.859C148.795 93.6064 151.137 92.4646 151.884 90.3471C152.236 89.3091 152.174 88.2088 151.697 87.2331C151.22 86.2574 150.391 85.51 149.375 85.1571L118.156 74.2373L115.461 81.96Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M86.4813 51.4015L111.958 60.3075C116.954 62.0513 119.587 67.5112 117.846 72.5143L103.356 114.138C101.614 119.141 96.1622 121.778 91.1663 120.034L65.6892 111.128C50.5564 105.834 42.5546 89.2676 47.8408 74.0921L49.5199 69.2758C54.7853 54.121 71.3278 46.1077 86.4813 51.4015Z",fill:"#77E0B5"}),(0,e.createElement)(M.Path,{d:"M94.3164 121.612C93.1348 121.612 91.9532 121.405 90.8131 121.01L65.336 112.104C49.6849 106.644 41.3722 89.4344 46.8449 73.7399L48.524 68.9236C53.976 53.2498 71.1611 44.9251 86.8329 50.4057L112.31 59.3117C114.984 60.2459 117.14 62.1766 118.363 64.7301C119.586 67.2835 119.752 70.1692 118.819 72.8472L104.329 114.471C103.396 117.149 101.468 119.308 98.9185 120.533C97.4674 121.259 95.8919 121.612 94.3164 121.612ZM76.9448 50.8209C65.3567 50.8209 54.515 58.0661 50.4934 69.6294L48.8142 74.4457C43.7354 89.04 51.4469 105.067 66.0408 110.153L91.5179 119.059C93.6738 119.806 95.9748 119.682 98.0271 118.685C100.079 117.689 101.613 115.966 102.38 113.807L116.871 72.1829C117.617 70.0238 117.493 67.7195 116.497 65.6642C115.502 63.609 113.782 62.0728 111.626 61.3047L86.1488 52.3986C83.0808 51.3191 79.9921 50.8209 76.9448 50.8209Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M117.906 95.5164C111.666 113.453 100.244 125.784 89.4228 122.006C89.402 122.006 86.7693 121.072 86.7486 121.072C84.5305 120.283 84.3647 117.19 86.4377 116.11C93.6724 112.332 99.6426 102.637 103.83 90.5963C108.018 78.5555 109.365 67.2414 106.048 59.7885C105.095 57.6295 107.126 55.3252 109.365 56.0933C109.386 56.0933 112.018 57.0275 112.039 57.0275C122.881 60.8058 124.166 77.5591 117.906 95.5164Z",fill:"#F582AE"}),(0,e.createElement)(M.Path,{d:"M93.4455 123.729C91.9737 123.729 90.5226 123.48 89.0922 122.981C89.0715 122.981 86.4181 122.047 86.3973 122.047C84.9877 121.549 84.0549 120.303 83.9512 118.809C83.8476 117.272 84.6146 115.882 85.9827 115.196C92.4505 111.833 98.4622 102.969 102.878 90.2638C107.293 77.5587 108.102 66.8881 105.116 60.2241C104.495 58.8332 104.743 57.2555 105.78 56.1344C106.796 55.0134 108.309 54.6397 109.719 55.1172C109.739 55.1172 110.817 55.5116 111.605 55.7815L112.393 56.0514C117.948 57.9821 121.41 63.1098 122.426 70.8325C123.338 77.8493 122.074 86.7346 118.902 95.869C115.73 105.003 111.19 112.747 106.111 117.667C101.966 121.674 97.6537 123.729 93.4455 123.729ZM108.495 56.9856C108.06 56.9856 107.625 57.1724 107.314 57.5253C106.941 57.9198 106.671 58.6256 107.003 59.373C111.107 68.5281 108.081 81.5861 104.826 90.9281C101.572 100.27 95.8295 112.373 86.9156 117.023C86.19 117.397 85.962 118.103 86.0035 118.643C86.0449 119.307 86.4595 119.847 87.0814 120.075C87.1436 120.096 89.7141 120.989 89.7556 121.009C95.7673 123.106 101.178 119.515 104.64 116.151C109.47 111.439 113.844 103.986 116.912 95.1631C119.98 86.3401 121.203 77.787 120.332 71.0816C119.71 66.2653 117.7 60.0996 111.688 58.0028L109.014 57.0686C108.848 57.0063 108.661 56.9856 108.495 56.9856Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M17.9661 58.8334C5.77689 51.0484 0.262729 43.6371 1.13339 36.2673C1.38215 34.129 2.48083 32.2607 4.28434 30.849C9.63266 26.6762 20.4744 27.4028 26.5068 28.1917C33.7416 28.9391 44.3346 27.9426 45.454 24.1227C46.1588 21.7146 42.8213 17.6041 35.2341 14.7185C20.9719 9.30015 6.15003 8.05455 6.00492 8.05455L6.56463 1.12073C7.20726 1.18301 22.5267 2.44936 37.701 8.24139C50.7401 13.203 53.6216 20.9672 52.1083 26.0949C50.6157 31.1811 45.4747 34.1913 36.8303 35.084C31.1918 35.6653 25.9471 35.1255 25.7191 35.1048L25.6154 35.084C16.4943 33.8592 10.4411 34.8349 8.51324 36.3296C8.03645 36.6825 8.01572 36.9316 7.99499 37.0562C7.35237 42.6199 15.6858 50.3218 30.8808 58.2106C39.8154 62.84 48.9781 66.4523 53.663 68.1961C55.4251 68.8604 56.3372 70.7911 55.736 72.5765C55.1141 74.4241 53.0826 75.3998 51.2376 74.7147C46.4076 72.9294 37.0584 69.2549 27.7299 64.4178C24.1229 62.5494 20.8475 60.681 17.9661 58.8334Z",fill:"white"}),(0,e.createElement)(M.Path,{d:"M52.4831 76.0022C51.9649 76.0022 51.4259 75.8984 50.9077 75.7115C46.1191 73.9469 36.6869 70.2309 27.2963 65.3523C23.6271 63.4424 20.2896 61.5533 17.3874 59.7056C4.84576 51.6923 -0.834239 43.9696 0.0986075 36.1431C0.388827 33.7349 1.61189 31.6174 3.64343 30.0396C9.3027 25.6385 20.4761 26.3651 26.6329 27.1748C34.1786 27.9429 43.6107 26.6765 44.4399 23.8532C44.9167 22.2339 42.2011 18.5179 34.8627 15.7153C20.7663 10.3385 6.06883 9.11362 5.92372 9.09286L4.88722 9.00982L5.59204 0L6.62854 0.0830398C7.27116 0.14532 22.7564 1.43244 38.0551 7.26598C51.7576 12.4975 54.722 20.843 53.0843 26.3859C51.4674 31.908 46.0361 35.1881 36.915 36.1223C31.1106 36.7244 25.8037 36.1638 25.5964 36.1431L25.4928 36.1223C16.6826 34.939 10.8574 35.8317 9.15759 37.1603C9.0954 37.2018 9.05394 37.2434 9.03321 37.2641C8.55642 42.3088 16.8899 49.7824 31.3801 57.2975C40.2732 61.9062 49.3944 65.4976 54.0586 67.2415C56.3596 68.0926 57.5412 70.6046 56.7535 72.9297C56.3596 74.0923 55.5304 75.0265 54.411 75.5662C53.8099 75.8569 53.1465 76.0022 52.4831 76.0022ZM17.7812 28.628C12.9719 28.628 7.95525 29.3131 4.90795 31.6797C3.33248 32.9045 2.3789 34.5446 2.15087 36.3922C1.3424 43.3468 6.69073 50.4052 18.5068 57.941C21.3468 59.7679 24.6428 61.6363 28.2499 63.5047C37.5576 68.321 46.8861 71.9955 51.6332 73.7601C52.2551 73.9885 52.9185 73.9677 53.5196 73.6771C54.1208 73.3864 54.5561 72.8882 54.7842 72.2654C55.2195 70.999 54.5769 69.6704 53.3331 69.1929C48.6274 67.4491 39.4233 63.8161 30.4265 59.1451C14.5888 50.9449 6.27613 43.0561 6.98094 36.9735C7.0224 36.5998 7.16751 36.1016 7.89306 35.541C10.4636 33.5481 17.4703 32.9668 25.7623 34.0878H25.8452C25.8866 34.0878 31.1313 34.6484 36.7284 34.0671C44.9167 33.2159 49.7468 30.4548 51.0942 25.8254C52.4831 21.1129 49.6846 13.9507 37.3088 9.23818C24.3526 4.27655 11.2513 2.63651 7.49919 2.26283L7.10532 7.12066C10.7331 7.5151 23.3576 9.11362 35.5675 13.7638C44.2119 17.0439 47.2385 21.6111 46.4093 24.4345C44.9374 29.4791 32.2715 29.8528 26.3634 29.2508C24.0417 28.9394 20.9529 28.628 17.7812 28.628Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M153.311 13.1766L151.457 14.105L158.651 28.5135L160.505 27.5851L153.311 13.1766Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M192.677 9.89165L183.835 28.8757L185.714 29.7533L194.556 10.7692L192.677 9.89165Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M216.3 46.1872L203.544 51.1431L204.294 53.0785L217.05 48.1227L216.3 46.1872Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M77.5319 172.045L66.3242 177.351L67.2102 179.227L78.4179 173.922L77.5319 172.045Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M101.208 190.757L90.2041 207.413L91.933 208.558L102.937 191.903L101.208 190.757Z",fill:"#2E58EC"}),(0,e.createElement)(M.Path,{d:"M131.628 193.373L129.754 194.26L134.829 205.015L136.704 204.128L131.628 193.373Z",fill:"#2E58EC"})),(0,e.createElement)("h4",null,(0,_.__)("Oops.. Results Not Found","gutenkit-blocks-addon")),(0,e.createElement)("p",null,(0,_.__)("Please ensure that your search is accurately spelled or attempt using different word","gutenkit-blocks-addon"))),A=window.wp.url;function N({type:t}){return(0,e.createElement)("div",{className:`loader-container ${"categories"===t?"load":""} ${"templates"===t?"template":""}`},"pages"===t||"templates"===t?Array.from({length:20},((t,a)=>(t=>(0,e.createElement)("svg",{key:t,className:"loader",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid meet",viewBox:"0 0 438 600",fill:"none"},(0,e.createElement)("defs",null,(0,e.createElement)("linearGradient",{id:"gradient1",x1:"0%",y1:"0%",x2:"100%",y2:"0%",gradientTransform:"translate(-2 0)"},(0,e.createElement)("stop",{offset:"0%",style:{stopColor:"#D7D8DD",stopOpacity:1}}),(0,e.createElement)("stop",{offset:"50%",style:{stopColor:"#E4E5EA",stopOpacity:1}}),(0,e.createElement)("stop",{offset:"100%",style:{stopColor:"#D7D8DD",stopOpacity:1}}),(0,e.createElement)("animateTransform",{attributeName:"gradientTransform",type:"translate",values:"-2 0; 0 0; 2 0",dur:"1.1s",repeatCount:"indefinite"}))),(0,e.createElement)("rect",{width:"438",height:"600",fill:"white"}),(0,e.createElement)("rect",{x:"10",y:"10",width:"418",height:"530",fill:"url(#gradient1)"}),(0,e.createElement)("rect",{x:"30",y:"558",width:"200",height:"8",rx:"4",fill:"url(#gradient1)"}),(0,e.createElement)("rect",{x:"30",y:"576",width:"120",height:"6",rx:"3",fill:"url(#gradient1)"}),(0,e.createElement)("circle",{cx:"393",cy:"570",r:"15",fill:"url(#gradient1)"})))(a))):"patterns"===t?Array.from({length:20},((t,a)=>(t=>(0,e.createElement)("svg",{key:t,className:"loader",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 362 270",fill:"none",preserveAspectRatio:"xMidYMid meet"},(0,e.createElement)("defs",null,(0,e.createElement)("linearGradient",{id:"gradient3",x1:"0%",y1:"0%",x2:"100%",y2:"0%",gradientTransform:"translate(-2 0)"},(0,e.createElement)("stop",{offset:"0%",style:{stopColor:"#D7D8DD",stopOpacity:1}}),(0,e.createElement)("stop",{offset:"50%",style:{stopColor:"#E4E5EA",stopOpacity:1}}),(0,e.createElement)("stop",{offset:"100%",style:{stopColor:"#D7D8DD",stopOpacity:1}}),(0,e.createElement)("animateTransform",{attributeName:"gradientTransform",type:"translate",values:"-2 0; 0 0; 2 0",dur:"1.1s",repeatCount:"indefinite"}))),(0,e.createElement)("rect",{width:"362",height:"270",fill:"white"}),(0,e.createElement)("rect",{x:"10",y:"10",width:"342",height:"220",fill:"url(#gradient3)"}),(0,e.createElement)("rect",{x:"111",y:"246",width:"140",height:"8",rx:"4",fill:"url(#gradient3)"})))(a))):"categories"===t?Array.from({length:20},((t,a)=>(t=>(0,e.createElement)("svg",{key:t,className:"loader category",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid meet",viewBox:"0 0 260 40",fill:"none"},(0,e.createElement)("defs",null,(0,e.createElement)("linearGradient",{id:"gradient2",x1:"0%",y1:"0%",x2:"100%",y2:"0%",gradientTransform:"translate(-2 0)"},(0,e.createElement)("stop",{offset:"0%",style:{stopColor:"#D7D8DD",stopOpacity:1}}),(0,e.createElement)("stop",{offset:"50%",style:{stopColor:"#E4E5EA",stopOpacity:1}}),(0,e.createElement)("stop",{offset:"100%",style:{stopColor:"#D7D8DD",stopOpacity:1}}),(0,e.createElement)("animateTransform",{attributeName:"gradientTransform",type:"translate",values:"-2 0; 0 0; 2 0",dur:"1.1s",repeatCount:"indefinite"}))),(0,e.createElement)("rect",{width:"260",height:"40",rx:"4",fill:"url(#gradient2)"}),(0,e.createElement)("rect",{x:"16",y:"16",width:"140",height:"8",rx:"4",fill:"url(#gradient2)"}),(0,e.createElement)("rect",{x:"230",y:"15",width:"14",height:"10",rx:"5",fill:"url(#gradient2)"})))(a))):null)}const I=e=>{const t={};for(const a in e){const r=e[a];r.hasOwnProperty("backgroundImage")&&r?.backgroundImage&&r?.backgroundImage?.url&&(t[a]=r),r.hasOwnProperty("compat")&&r.hasOwnProperty("filename")&&(t[a]=r),"blockID"===a&&(t[a]=r),Array.isArray(r)&&r.length>0&&r.some((e=>e.hasOwnProperty("backgroundImage")&&e?.backgroundImage?.url))&&(t[a]=r)}return Object.keys(t).length>1?t:null},H=e=>{if(!e||!e.length)return;let t=[];for(const a in e){let r=e[a];r.hasOwnProperty("backgroundImage")&&r?.backgroundImage?.url&&t.push({backgroundImage:r?.backgroundImage}),r.hasOwnProperty("image")&&r?.image?.url&&t.push({image:r})}return t.length>0?t:null},x=async e=>{try{return await S()({path:"/gutenkit/v1/media-upload-from-url",method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})}catch(e){throw e}},V=async e=>{if(!e)return;const t=[];for(const a of e){if(a?.attributes){const e=I(a.attributes);if(e)for(const t of Object.keys(e)){let r=e[t];if("blockID"!==t&&r.hasOwnProperty("backgroundImage")&&r?.backgroundImage){const e=await x(r?.backgroundImage);e&&(a.attributes[t]={...r,backgroundImage:{...e,imageUrl:e.url}})}if("blockID"!==t&&r.hasOwnProperty("compat")&&r.hasOwnProperty("filename")){const e=await x(r);e&&(a.attributes[t]=e)}if("blockID"!==t&&Array.isArray(r)&&r.length>0){let e=t,n=r;const l=H(n),i=[];if(l)for(const e in n){let t=n[e],a=l[e];if(a&&a.hasOwnProperty("backgroundImage")&&a?.backgroundImage){const e=await x(a?.backgroundImage);e&&i.push({...t,backgroundImage:e})}if(a&&a.hasOwnProperty("image")&&a?.image){const e=await x(a?.image);e&&i.push({...t,image:e})}}a.attributes[e]=i}}}a.innerBlocks&&a.innerBlocks.length>0&&(a.innerBlocks=await V(a.innerBlocks)),t.push(a)}return t},R=V,O=(0,t.memo)((()=>{const{dispatch:a,searchInput:r,imageImportType:n}=s(),{loading:l,loadMoreRef:i,pages:o,hasMore:c}=(()=>{const{templateType:e,dispatch:a,pages:r,syncLibrary:n,searchInput:l,pageTemplatesPage:i,pageHasMore:o,payload:c}=s(),[C,p]=(0,t.useState)(!1),m=(0,t.useRef)(null);return(0,t.useEffect)((()=>{n&&(a({type:"SET_PAGES",pages:[]}),a({type:"SET_PAGE_TEMPLATES_PAGE",pageTemplatesPage:1}),a({type:"SET_PAGE_HAS_MORE",pageHasMore:!0}));const t=async()=>{try{r&&0===r.length&&p(!0);let t={page:i,per_page:16};if("pages"===e)if(""===l){const e=(0,A.addQueryArgs)("https://wpmet.com/plugin/gutenkit/wp-json/gkit/v1/layout-manager-api/pages",t),n=await fetch(e,{method:"POST",body:JSON.stringify(c)}),l=await n.json();a({type:"SET_PAGES",pages:[...r,...l?.posts]}),l?.posts.length<t.per_page?a({type:"SET_PAGE_HAS_MORE",pageHasMore:!1}):a({type:"SET_PAGE_TEMPLATES_PAGE",pageTemplatesPage:i+1})}else{t.search=l.toLowerCase(),t.page=1,t.per_page=100;const e=(0,A.addQueryArgs)("https://wpmet.com/plugin/gutenkit/wp-json/gkit/v1/layout-manager-api/pages",t),r=await fetch(e,{method:"POST",body:JSON.stringify(c)}),n=await r.json();a({type:"SET_PAGES",pages:n?.posts}),a({type:"SET_PAGE_TEMPLATES_PAGE",pageTemplatesPage:1}),a({type:"SET_PAGE_HAS_MORE",pageHasMore:!1})}}catch(e){console.error(`Error fetching pages: ${e}`),p(!1)}finally{p(!1)}};""!==l&&t();const s=new IntersectionObserver((e=>{e[0].isIntersecting&&o&&""===l&&t()}));return s&&m.current&&s.observe(m.current),()=>{s&&s.disconnect(),a({type:"SET_SYNC_LIBRARY",syncLibrary:!1})}}),[e,l,n,i]),{loading:C,loadMoreRef:m,pages:r,hasMore:o}})(),{insertBlocks:C}=(0,d.dispatch)("core/block-editor");(0,t.useEffect)((()=>{S()({path:"/gutenkit/v1/settings"}).then((e=>{const t="active"===e.settings.remote_image.status?"upload":"";a({type:"SET_IMAGE_IMPORT_TYPE",imageImportType:t})}))["catch"]((e=>{console.warn("Fetch failed: ",e.message)}))}),[]);const p=async e=>{const t=(0,v.parse)(e.content);if("upload"===n){const e=await R(t);C(e)}else C(t);await a({type:"SET_LOAD_LIBRARY",loadLibrary:!1})};return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:"gutenkit-library-list gutenkit-page"},(0,e.createElement)("div",{className:"gutenkit-library-list__header"},(0,e.createElement)("h2",{className:"gutenkit-library-list__title"},(0,_.__)("Pages","gutenkit-blocks-addon"))),(0,e.createElement)("ul",{className:"gutenkit-library-list__items"},o&&0===o.length&&l?(0,e.createElement)(N,{type:"pages"}):o&&o.map(((t,a)=>(0,e.createElement)(T,{key:t?.ID,page:t,handlePageImport:p})))),c&&(0,e.createElement)("button",{className:"has-more-data",ref:i}),o&&0===o.length&&""!==r&&(0,e.createElement)(P,null)))})),Z=()=>{const{filter:a,dispatch:r}=s(),{categories:n,loading:l}=(()=>{const[e,a]=(0,t.useState)(!1),{dispatch:r,categories:n,templateType:l,payload:i}=s();return(0,t.useEffect)((()=>{(async()=>{if("patterns"===l&&0===n.length)try{a(!0);const e=await fetch("https://wpmet.com/plugin/gutenkit/wp-json/gkit/v1/layout-manager-api/patterns/categories",{method:"POST",body:JSON.stringify(i)}),t=await e.json(),n=[{id:0,title:"All",slug:"all",count:t.reduce(((e,t)=>e+t.count),0)},...t];r({type:"SET_CATEGORIES",categories:n})}catch(e){console.error(`Error fetching categories: ${e}`),a(!1)}finally{a(!1)}})()}),[]),{categories:n,loading:e}})();return(0,e.createElement)("div",{className:"gutenkit-library-filter"},n&&0===n.length&&l?(0,e.createElement)(N,{type:"categories"}):(0,e.createElement)("div",{className:"gutenkit-library-filter__inner"},(0,e.createElement)("h3",{className:"gutenkit-library-filter-title"},(0,_.__)("Category","gutenkit-blocks-addon")),(0,e.createElement)("ul",{className:"gutenkit-library-filter-category-list"},n.map(((t,n)=>(0,e.createElement)("li",{key:n,className:"gutenkit-library-filter-category-list-item"},(0,e.createElement)("button",{className:"gutenkit-library-filter-category-list-title "+(t.slug===a?.category?"is-active":""),onClick:()=>{return e=t.slug,r({type:"SET_SEARCH_INPUT",searchInput:""}),r({type:"SET_KEY_WORDS",keyWords:""}),void r({type:"SET_FILTER",filter:{...a,category:e}});var e}},(0,e.createElement)("span",null,t.title),(0,e.createElement)("span",{className:"list-title-count"},t.count))))))))},B=function({pattern:a,handlePatternImport:n}){const[i,s]=(0,t.useState)(!1),{status:o,handlePluginInstall:c,importButtonRef:C}=b(a,n,s);(0,t.useEffect)((()=>{i&&document.querySelectorAll(".gutenkit-library-list-item-inner-content-thumbnail:not(.is-loading)").forEach((e=>{e.classList.add("disabled")}))}),[i]);const p=l()("gutenkit-library-list-item-inner-content-thumbnail",{"is-loading":i}),m=l()("gutenkit-library-list-item",{"pro-inactive":"pro"===a?.["package"]&&!0}),u=l()("gutenkit-library-list-item__title",{"is-premium":"pro"===a?.["package"]});return(0,e.createElement)("div",{className:m,key:a?.ID},(0,e.createElement)("div",{className:p},(0,e.createElement)(w,{src:a?.thumbnail,alt:a?.title}),(0,e.createElement)("div",{className:"gutenkit-library-list-item-inner-content-overlay"},"pro"===a?.["package"]&&!1,"pro"===a?.["package"]&&(0,e.createElement)(f,{href:"https://wpmet.com/plugin/gutenkit/"},(0,_.__)("Requires GutenKit Blocks PRO","gutenkit-blocks-addon")),"free"===a?.["package"]&&"Activated"===o&&(0,e.createElement)(r.Button,{ref:C,onClick:async()=>{s(!0),await n(a),s(!1)},className:"gutenkit-import-button",icon:i?(0,e.createElement)(r.Spinner,{className:"importing-spinner"}):(0,e.createElement)(E,null),disabled:!!i},i?(0,_.__)("Importing","gutenkit-blocks-addon"):(0,_.__)("Import","gutenkit-blocks-addon")),"free"===a?.["package"]&&"Activated"!==o&&(0,e.createElement)(r.Button,{onClick:c,className:"gutenkit-import-button",icon:"Installing"===o||"Activating"===o?(0,e.createElement)(r.Spinner,{className:"importing-spinner"}):(0,e.createElement)(E,null),disabled:!!i},(0,_.__)(`${o}`,"gutenkit-blocks-addon")))),(0,e.createElement)("div",{className:u},(0,e.createElement)("span",{className:"item-title"},a?.title)))},G=()=>{const{dispatch:a,searchInput:r,imageImportType:n,filter:l}=s(),{patterns:i,loading:o,loadMoreRef:c,hasMore:C}=(()=>{const{templateType:e,dispatch:a,patterns:r,syncLibrary:n,searchInput:l,filter:i,patternsPage:o,payload:c}=s(),[C,p]=(0,t.useState)(!1),[m,u]=(0,t.useState)(!0),g=(0,t.useRef)(null);return(0,t.useEffect)((()=>{n&&(a({type:"SET_PATTERNS",patterns:[]}),a({type:"SET_PATTERNS_PAGE",patternsPage:1}),u(!0));const t={...c,auth:"other"},s=async()=>{try{r&&0===r.length&&p(!0);let n={page:o,per_page:20};if("patterns"===e)if(""===l)if("all"===i.category){const e=(0,A.addQueryArgs)("https://wpmet.com/plugin/gutenkit/wp-json/gkit/v1/layout-manager-api/patterns",n),l=await fetch(e,{method:"POST",body:JSON.stringify(t)}),i=await l.json();a({type:"SET_PATTERNS",patterns:[...r,...i?.posts]}),i?.posts.length<n.per_page?u(!1):a({type:"SET_PATTERNS_PAGE",patternsPage:o+1})}else{n.cat=i.category,n.page=1,n.per_page=50;const e=(0,A.addQueryArgs)("https://wpmet.com/plugin/gutenkit/wp-json/gkit/v1/layout-manager-api/patterns",n),t=await fetch(e,{method:"POST",body:JSON.stringify(c)}),r=await t.json();a({type:"SET_PATTERNS",patterns:r?.posts}),a({type:"SET_PATTERNS_PAGE",patternsPage:1})}else{a({type:"SET_FILTER",filter:{}}),n.search=l.toLowerCase(),n.page=1,n.per_page=100;const e=(0,A.addQueryArgs)("https://wpmet.com/plugin/gutenkit/wp-json/gkit/v1/layout-manager-api/patterns",n),t=await fetch(e,{method:"POST",body:JSON.stringify(c)}),r=await t.json();a({type:"SET_PATTERNS",patterns:r?.posts}),a({type:"SET_PATTERNS_PAGE",patternsPage:1})}}catch(e){console.error(`Error fetching patterns: ${e}`),p(!1)}finally{p(!1)}};"all"===i.category&&""===l||s();const C=new IntersectionObserver((e=>{e[0].isIntersecting&&m&&"all"===i.category&&s()}));return C&&g.current&&C.observe(g.current),()=>{C&&C.disconnect(),a({type:"SET_SYNC_LIBRARY",syncLibrary:!1})}}),[e,l,i.category,n,o]),{patterns:r,loading:C,loadMoreRef:g,hasMore:m}})(),{insertBlocks:p,insertAfterBlock:m,replaceBlocks:u}=(0,d.dispatch)("core/block-editor"),{getSelectedBlockClientId:g}=(0,d.select)("core/block-editor");(0,t.useEffect)((()=>{S()({path:"/gutenkit/v1/settings"}).then((e=>{const t="active"===e.settings.remote_image.status?"upload":"";a({type:"SET_IMAGE_IMPORT_TYPE",imageImportType:t})}))["catch"]((e=>{console.warn("Fetch failed: ",e.message)}))}),[]);const E=async e=>{const t=(0,v.parse)(e.content),r=g();if("upload"===n){const e=await R(t);if(r){m(r);const t=g();u(t,e)}else p(e)}else if(r){m(r);const e=g();u(e,t)}else p(t);await a({type:"SET_LOAD_LIBRARY",loadLibrary:!1})};return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Z,null),(0,e.createElement)("div",{className:"gutenkit-library-list gutenkit-pattern-part"},(0,e.createElement)("div",{className:"gutenkit-pattern"},i&&0===i.length&&o?(0,e.createElement)(N,{type:"patterns"}):(0,e.createElement)(e.Fragment,null,i&&i.map(((t,a)=>(0,e.createElement)(B,{key:t?.ID,pattern:t,handlePatternImport:E}))))),C&&"all"===l.category&&(0,e.createElement)("button",{className:"has-more-data",ref:c}),i&&0===i.length&&""!==r&&(0,e.createElement)(P,null)))},F=(0,t.memo)((({template:t})=>{const{dispatch:a}=s(),r=l()("gutenkit-library-list-item-inner-content-thumbnail"),n=l()("gutenkit-library-list-item__title",{"is-premium":"pro"===t?.["package"]});return(0,e.createElement)("li",{key:t?.ID,className:"gutenkit-library-list-item",onClick:()=>{a({type:"SET_SINGLE_TEMPLATE",singleTemplate:t}),a({type:"SET_IS_SINGLE_PAGE",showSinglePage:!0})}},(0,e.createElement)("div",{className:r},(0,e.createElement)(w,{src:t?.thumbnail,alt:t?.title})),(0,e.createElement)("div",{className:"list-item-title-flex"},(0,e.createElement)("div",{className:n},(0,e.createElement)("div",{className:"item-title"},t?.title),t?.count&&(0,e.createElement)("span",{className:"counter"},t?.count," Pages")),(0,e.createElement)("button",{className:"favorite-icon",style:{opacity:0}},(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"38",height:"38",viewBox:"0 0 38 38",fill:"none"},(0,e.createElement)("rect",{x:"0.5",y:"0.5",width:"37",height:"37",rx:"18.5",fill:"white",stroke:"#EEEFF4"}),(0,e.createElement)("path",{d:"M24.9701 12.7953C22.8247 11.4794 20.9523 12.0097 19.8275 12.8544C19.3662 13.2008 19.1357 13.3739 19 13.3739C18.8643 13.3739 18.6338 13.2008 18.1725 12.8544C17.0477 12.0097 15.1753 11.4794 13.03 12.7953C10.2145 14.5224 9.57738 20.2199 16.0716 25.0267C17.3086 25.9422 17.927 26.4 19 26.4C20.073 26.4 20.6914 25.9422 21.9284 25.0267C28.4226 20.2199 27.7855 14.5224 24.9701 12.7953Z",stroke:"#111722",strokeWidth:"1.5",strokeLinecap:"round"})))))}));function D({count:t}){return(0,e.createElement)("div",{className:"gutenkit-library__favorite"},(0,e.createElement)("span",{className:"gutenkit-library__favorite-icon"},(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"15",viewBox:"0 0 16 15",fill:"none"},(0,e.createElement)("path",{d:"M13.2238 1.69591C11.3466 0.544462 9.70828 1.00848 8.72408 1.74761C8.32046 2.05067 8.11872 2.2022 8 2.2022C7.88128 2.2022 7.67954 2.05067 7.27592 1.74761C6.29173 1.00848 4.65336 0.544462 2.77621 1.69591C0.312648 3.20706 -0.244796 8.19243 5.43767 12.3984C6.52 13.1995 7.06116 13.6 8 13.6C8.93884 13.6 9.48001 13.1995 10.5623 12.3984C16.2448 8.19243 15.6873 3.20706 13.2238 1.69591Z",stroke:"#111722",strokeWidth:"1.5",strokeLinecap:"round"}))),(0,e.createElement)("span",{className:"gutenkit-library__favorite-text"},(0,_.__)(`Favorites ( ${t} )`,"gutenkit-blocks-addon")))}const j=()=>{const{searchInput:a}=s(),{loading:r,loadMoreRef:n,templates:l,hasMore:i}=(()=>{const{templateType:e,dispatch:a,templates:r,syncLibrary:n,searchInput:l,templatesPage:i,templatesHasMore:o,payload:c}=s(),[C,p]=(0,t.useState)(!1),m=(0,t.useRef)(null);return(0,t.useEffect)((()=>{n&&(a({type:"SET_TEMPLATES",templates:[]}),a({type:"SET_TEMPLATES_PAGE",templatesPage:1}),a({type:"SET_TEMPLATES_HAS_MORE",templatesHasMore:!0}));const t=async()=>{try{r&&0===r.length&&p(!0);let t={page:i,per_page:16};if("templates"===e)if(""===l){const e=(0,A.addQueryArgs)("https://wpmet.com/plugin/gutenkit/wp-json/gkit/v1/layout-manager-api/templates",t),n=await fetch(e,{method:"POST",body:JSON.stringify(c)}),l=await n.json();a({type:"SET_TEMPLATES",templates:[...r,...l?.posts]}),l?.posts.length<t.per_page?a({type:"SET_TEMPLATES_HAS_MORE",templatesHasMore:!1}):a({type:"SET_TEMPLATES_PAGE",templatesPage:i+1})}else{t.search=l.toLowerCase(),t.page=1,t.per_page=100;const e=(0,A.addQueryArgs)("https://wpmet.com/plugin/gutenkit/wp-json/gkit/v1/layout-manager-api/templates",t),r=await fetch(e,{method:"POST",body:JSON.stringify(c)}),n=await r.json();a({type:"SET_TEMPLATES",templates:n.posts}),a({type:"SET_TEMPLATES_HAS_MORE",templatesHasMore:!1}),a({type:"SET_TEMPLATES_PAGE",templatesPage:1})}}catch(e){console.error(`Error fetching templates: ${e}`),p(!1)}finally{p(!1)}};""!==l&&t();const s=new IntersectionObserver((e=>{e[0].isIntersecting&&o&&""===l&&t()}));return s&&m.current&&s.observe(m.current),()=>{s&&s.disconnect(),a({type:"SET_SYNC_LIBRARY",syncLibrary:!1})}}),[e,l,n,i]),{loading:C,loadMoreRef:m,templates:r,hasMore:o}})();return(0,e.createElement)("div",{className:"gutenkit-library-list gutenkit-template"},(0,e.createElement)("div",{className:"gutenkit-library-list__header"},(0,e.createElement)("h2",{className:"gutenkit-library-list__title"},(0,_.__)("Template Kits","gutenkit-blocks-addon")),(0,e.createElement)(D,{count:6})),l&&0===l.length&&r?(0,e.createElement)(N,{type:"templates"}):(0,e.createElement)("ul",{className:"gutenkit-library-list__items"},l&&l.map(((t,a)=>(0,e.createElement)(F,{key:t?.ID,template:t})))),i&&(0,e.createElement)("button",{className:"has-more-data",ref:n}),l&&0===l.length&&""!==a&&(0,e.createElement)(P,null))};function U(){return(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 12 12",fill:"none"},(0,e.createElement)("g",{id:"icon"},(0,e.createElement)("path",{id:"Vector",d:"M4.125 7.25L1 4.125L4.125 1",stroke:"#111722",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,e.createElement)("path",{id:"Vector_2",d:"M11 11V6.625C11 5.96196 10.7366 5.32607 10.2678 4.85723C9.79893 4.38839 9.16304 4.125 8.5 4.125H1",stroke:"#111722",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})))}function Y(){return(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"22",height:"20",viewBox:"0 0 22 20",fill:"none"},(0,e.createElement)("g",{id:"pro_icon"},(0,e.createElement)("path",{id:"Crown",d:"M18.9068 18.0959H3.30584V20H18.9068V18.0959ZM1.31579 6.45307L3.18237 16.1677H19.0318L20.9018 6.43281C20.9452 6.43942 20.989 6.4429 21.0328 6.4432C21.2705 6.44394 21.5001 6.35344 21.6775 6.18906C21.8549 6.02468 21.9676 5.798 21.994 5.55253C22.0204 5.30706 21.9587 5.06009 21.8207 4.85903C21.6826 4.65796 21.478 4.51697 21.246 4.4631C21.014 4.40924 20.7711 4.44629 20.5638 4.56717C20.3566 4.68804 20.1996 4.8842 20.1229 5.11802C20.0463 5.35183 20.0555 5.60682 20.1487 5.83404C20.2419 6.06126 20.4125 6.24471 20.6279 6.34917C19.7781 7.94351 17.887 10.9675 15.755 10.9675C13.0301 10.9675 11.6374 4.49249 11.244 2.29709C11.5246 2.26632 11.7832 2.12553 11.9674 1.90343C12.1515 1.68132 12.2472 1.39461 12.2349 1.10173C12.2227 0.80885 12.1034 0.531846 11.9015 0.327172C11.6995 0.122499 11.43 0.00556158 11.148 0.000193325C10.866 -0.00517493 10.5926 0.10143 10.3835 0.298281C10.1745 0.495132 10.0456 0.767412 10.023 1.05963C10.0004 1.35185 10.086 1.642 10.2621 1.87097C10.4383 2.09993 10.6918 2.25047 10.9711 2.29189C10.5787 4.48158 9.1865 10.9675 6.45912 10.9675C4.33111 10.9675 2.44254 7.95442 1.59123 6.35852C1.84466 6.23123 2.04202 6.00853 2.14327 5.73559C2.24453 5.46265 2.24211 5.15991 2.13649 4.88876C2.03088 4.61761 1.82999 4.39836 1.57455 4.27546C1.31912 4.15257 1.02828 4.13524 0.761 4.22699C0.493726 4.31873 0.270033 4.51269 0.135291 4.76952C0.000548589 5.02634 -0.0351561 5.32681 0.0354181 5.60999C0.105992 5.89317 0.277562 6.13785 0.515331 6.29441C0.753099 6.45097 1.03927 6.50769 1.31579 6.45307Z",fill:"url(#paint0_linear_1707_1666)"})),(0,e.createElement)("defs",null,(0,e.createElement)("linearGradient",{id:"paint0_linear_1707_1666",x1:"11",y1:"20",x2:"11",y2:"-7.21941e-07",gradientUnits:"userSpaceOnUse"},(0,e.createElement)("stop",{stopColor:"#FF1B6C"}),(0,e.createElement)("stop",{offset:"1",stopColor:"#FFAE11"}))))}const W=()=>{const{singleTemplate:a,syncLibrary:n,dispatch:i,imageImportType:o,payload:c}=s(),{insertBlocks:C}=(0,d.dispatch)("core/block-editor"),[p,m]=(0,t.useState)([]),[u,g]=(0,t.useState)(p[0]),[h,y]=(0,t.useState)(!1),[k,w]=(0,t.useState)(!1),L=u,T="pro"===a?.["package"];(0,t.useEffect)((()=>{y(!0);const e={id:a?.ID},t=(0,A.addQueryArgs)("https://wpmet.com/plugin/gutenkit/wp-json/gkit/v1/layout-manager-api/templates",e);return fetch(t,{method:"POST",body:JSON.stringify(c)}).then((e=>e.json())).then((e=>{e?.posts&&(e.posts.sort(((e,t)=>e.title.toLowerCase().includes("home")?-1:t.title.toLowerCase().includes("home")?1:0)),m(e.posts),g(e.posts[0]))}))["catch"]((e=>{console.error(`Error fetching pages: ${e}`)}))["finally"]((()=>{y(!1)})),()=>{i({type:"SET_SYNC_LIBRARY",syncLibrary:!1})}}),[a,n]),(0,t.useEffect)((()=>{S()({path:"/gutenkit/v1/settings"}).then((e=>{const t="active"===e.settings.remote_image.status?"upload":"";i({type:"SET_IMAGE_IMPORT_TYPE",imageImportType:t})}))["catch"]((e=>{console.warn("Fetch failed: ",e.message)}))}),[]);const M=async e=>{w(!0);const t=(0,v.parse)(e.content);if("upload"===o){const e=await R(t);C(e)}else C(t);w(!1),i({type:"SET_LOAD_LIBRARY",loadLibrary:!1}),i({type:"SET_IS_SINGLE_PAGE",showSinglePage:!1})},{status:P,handlePluginInstall:N,importButtonRef:I}=b(L,M,w),H=l()("gutenkit-library-single-page__current-thumbnail",{"is-premium":"pro"===a?.["package"]});return(0,e.createElement)("div",{key:0,className:"gutenkit-library-single-page"},h&&(0,e.createElement)("div",{className:"gutenkit-library-single-page__spinner"},(0,e.createElement)("button",{className:"has-more-data"})),(0,e.createElement)("div",{className:"gutenkit-library-single-page__inner"},L&&(0,e.createElement)("div",{className:"gutenkit-library-single-page__current",key:0},(0,e.createElement)("div",{className:"gutenkit-library-single-page__current-header"},(0,e.createElement)("button",{className:"gutenkit-library-single-page__current-back",onClick:()=>(i({type:"SET_IS_SINGLE_PAGE",showSinglePage:!1}),void i({type:"SET_TEMPLATE_TYPE",templateType:"templates"}))},(0,e.createElement)(U,null)," Back"),(0,e.createElement)("h3",{className:"gutenkit-library-single-page__current-title"},L.title)),(0,e.createElement)("div",{className:"gutenkit-library-single-page__current-scroll"},(0,e.createElement)("div",{className:H},(0,e.createElement)("img",{src:L.screenshot,alt:L.title,width:1200,height:1200})))),(0,e.createElement)("div",{className:"gutenkit-library-single-page__group"},(0,e.createElement)("div",{className:"gutenkit-library-single-page__group-header"},(0,e.createElement)("h3",{className:"gutenkit-library-single-page__group-title"},(0,_.__)(`Pages (${p.length})`,"gutenkit-blocks-addon")),(0,e.createElement)(D,{count:6})),(0,e.createElement)("div",{className:"gutenkit-library-single-page__group-scroll"},(0,e.createElement)("div",{className:"gutenkit-library-single-page__group-items"},!h&&p.map(((t,a)=>(0,e.createElement)("div",{key:a,onClick:()=>(e=>{g(p[e])})(a),className:"gutenkit-library-single-page__group-item "+(L?.ID===t.ID?"is-active":"")},(0,e.createElement)("div",{className:"gutenkit-library-single-page__group-item-inner"},(0,e.createElement)("div",{className:"gutenkit-library-single-page__group-item-thumbnail"},(0,e.createElement)("img",{width:400,height:400,src:t.screenshot,alt:t.title})),(0,e.createElement)("div",{className:"gutenkit-library-single-page__group-item-content"},(0,e.createElement)("h3",{className:"gutenkit-library-single-page__group-item-title"},t.title)))))))),(0,e.createElement)("div",{className:"gutenkit-library-single-page__pro-notice"},T&&(0,e.createElement)("div",{className:"gutenkit-library-single-page__pro-notice-inner"},(0,e.createElement)("div",{className:"gutenkit-library-single-page__pro-notice-icon"},(0,e.createElement)(Y,null),(0,e.createElement)("h3",null,(0,_.__)("Premium Template","gutenkit-blocks-addon"))),(0,e.createElement)("p",null,(0,_.__)("Unlock access to this template, along with a vast array of others, for as little as $49. Start creating effortlessly today!","gutenkit-blocks-addon"))),(0,e.createElement)(r.ButtonGroup,null,(0,e.createElement)(f,{href:L?.live_preview,icon:!1},(0,_.__)("Live Preview","gutenkit-blocks-addon")),T&&(0,e.createElement)(f,{href:"https://wpmet.com/plugin/gutenkit/pricing"},(0,_.__)("Upgrade Now!","gutenkit-blocks-addon")),T&&!1,"free"===a?.["package"]&&"Activated"===P&&(0,e.createElement)(r.Button,{ref:I,onClick:async()=>{w(!0),await M(L),w(!1)},className:"gutenkit-import-button",icon:k?(0,e.createElement)(r.Spinner,{className:"importing-spinner"}):(0,e.createElement)(E,null),disabled:!!k},k?(0,_.__)("Importing","gutenkit-blocks-addon"):(0,_.__)("Import","gutenkit-blocks-addon")),"free"===a["package"]&&"Activated"!==P&&(0,e.createElement)(r.Button,{onClick:N,className:"gutenkit-import-button",icon:"Installing"===P||"Activating"===P?(0,e.createElement)(r.Spinner,{className:"importing-spinner"}):(0,e.createElement)(E,null),disabled:!!k},(0,_.__)(`${P}`,"gutenkit-blocks-addon")))))))},$=()=>{const{showSinglePage:t,templateType:a}=s();return(0,e.createElement)("div",{className:"interface-interface-skeleton__body gutenkit-library-body"},(0,e.createElement)("div",{className:"interface-interface-skeleton__content gutenkit-library-body-content"},(0,e.createElement)("div",{className:"gutenkit-library-body-content__inner"},t?(0,e.createElement)(W,null):"pages"===a?(0,e.createElement)(O,null):"patterns"===a?(0,e.createElement)(G,null):"templates"===a?(0,e.createElement)(j,null):"")))},J=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 18",width:"16",height:"16","aria-hidden":"true",focusable:"false"},(0,e.createElement)("path",{fill:"url(#a)",d:"M16.146 5.695c-.284-.214-.711-.285-1.067-.214-.355.07-.711.284-.924.64-.996 1.565-3.343 1.636-3.343 1.636h-.071c-3.7 0-5.122 3.201-5.193 3.343-.284.712 0 1.565.712 1.85.142.07.355.142.569.142.569 0 1.067-.355 1.351-.853.356-.64.854-1.138 1.566-1.423v2.56c-.071.499-.356.925-.783 1.21-.498.355-1.28.569-2.205.569-1.067 0-1.92-.356-2.56-1.067-.64-.712-.997-1.85-.997-3.272V6.973c.071-1.28.356-2.205.996-2.845.64-.711 1.494-1.067 2.561-1.067.924 0 1.636.214 2.205.57.427.284.712.782.783 1.35v.072a1.61 1.61 0 0 0 1.636 1.636 1.61 1.61 0 0 0 1.636-1.636v-.356a4.394 4.394 0 0 0-.64-1.92c-.355-.57-.783-1.138-1.352-1.494C9.888.431 8.466.004 6.758.004 4.766.004 3.13.715 1.85 1.995.712 3.275.07 4.912 0 6.833v3.912c0 2.134.64 3.912 1.85 5.263C3.059 17.36 4.766 18 6.758 18c1.636 0 3.059-.427 4.196-1.21a4.345 4.345 0 0 0 1.921-3.129l.071-3.485c1.138-.356 2.56-1.068 3.486-2.49.213-.284.355-.712.284-1.067 0-.355-.284-.712-.569-.924Z"}),(0,e.createElement)("path",{fill:"url(#b)",d:"M9.744 4.841c0 .925.711 1.707 1.636 1.707a1.61 1.61 0 0 0 1.636-1.636V4.7a4.394 4.394 0 0 0-.64-1.921c-.355-.569-.64-.924-1.352-1.493C9.174-.138 6.756.004 6.756.004 9.175.928 9.744 3.988 9.744 4.84Z"}),(0,e.createElement)("path",{fill:"url(#c)",d:"M16.146 5.695c-.284-.214-.711-.285-1.067-.214-.355.071-.712.285-.924.64-.996 1.565-3.343 1.636-3.343 1.636h-.071c-3.7 0-5.122 3.201-5.193 3.343-.284.712 0 1.565.712 1.85.142.07.355.143.569.143.569 0 1.067-.356 1.351-.854 0-.07.57-1.067 1.566-1.422 1.208-.428 1.777-.071 3.272-.57 1.138-.355 2.56-1.066 3.485-2.489.214-.284.356-.712.285-1.067-.07-.427-.355-.783-.64-.996h-.002Z"}),(0,e.createElement)("path",{fill:"url(#d)",d:"M16.146 5.695c-.284-.214-.711-.285-1.067-.214-.355.071-.712.285-.924.64-.996 1.565-3.415 1.636-3.415 1.636 2.49-.427 3.557 3.201 5.762 0 .213-.284.355-.712.284-1.067-.07-.427-.355-.783-.64-.996Z"}),(0,e.createElement)("defs",null,(0,e.createElement)("linearGradient",{id:"a",x1:"-.001",x2:"16.775",y1:"9.002",y2:"9.002",gradientUnits:"userSpaceOnUse"},(0,e.createElement)("stop",{stopColor:"#007BFF"}),(0,e.createElement)("stop",{offset:"1",stopColor:"#221377"})),(0,e.createElement)("linearGradient",{id:"b",x1:"9.824",x2:"13.376",y1:"2.303",y2:"7.404",gradientUnits:"userSpaceOnUse"},(0,e.createElement)("stop",{stopColor:"#007BFF"}),(0,e.createElement)("stop",{offset:"1",stopColor:"#221377"})),(0,e.createElement)("linearGradient",{id:"c",x1:"6.589",x2:"15.857",y1:"12.068",y2:"6.029",gradientUnits:"userSpaceOnUse"},(0,e.createElement)("stop",{stopColor:"#FC9257"}),(0,e.createElement)("stop",{offset:"1",stopColor:"#F05A24"})),(0,e.createElement)("linearGradient",{id:"d",x1:"15.046",x2:"13.001",y1:"7.049",y2:"8.369",gradientUnits:"userSpaceOnUse"},(0,e.createElement)("stop",{stopColor:"#FFA44D"}),(0,e.createElement)("stop",{offset:"1",stopColor:"#FBD06A"})))),q=function({className:a}){const{dispatch:n,loadLibrary:i}=s(),o=(0,t.useCallback)((()=>{n({type:"SET_LOAD_LIBRARY",loadLibrary:!i})}),[n,i]);return(0,t.useEffect)((()=>{const e=document.querySelector(".interface-interface-skeleton__editor:not(.gutenkit-template-library)"),t=document.querySelector(".edit-site-layout .edit-site-layout__hub");i?(e?.classList.add("hide-editor"),t?.classList.add("hide-editor")):(e?.classList.remove("hide-editor"),t?.classList.remove("hide-editor"))}),[i]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)(r.Button,{icon:(0,e.createElement)(J,null),iconSize:16,onClick:o,className:"gutenkit-template-library-btn",variant:"primary"},"Template Library"),i&&(0,t.createPortal)((0,e.createElement)("div",{className:l()("interface-interface-skeleton__editor","gutenkit-template-library",a)},(0,e.createElement)(g,null),(0,e.createElement)($,null)),document.querySelector(".interface-interface-skeleton")))},K={loadLibrary:!1,syncLibrary:!1,searchInput:"",templateType:"patterns",pages:[],showSinglePage:!1,singleTemplate:{},patterns:[],templates:[],favorite:[],filter:{category:"all"},categories:[],imageImportType:"upload",patternsPage:1,pageTemplatesPage:1,templatesPage:1,templatesHasMore:!0,pageHasMore:!0,keyWords:""},Q=(e,t)=>{switch(t.type){case"SET_LOAD_LIBRARY":return{...e,loadLibrary:t.loadLibrary};case"SET_SYNC_LIBRARY":return{...e,syncLibrary:t.syncLibrary};case"SET_SEARCH_INPUT":return{...e,searchInput:t.searchInput};case"SET_TEMPLATE_TYPE":return{...e,templateType:t.templateType};case"SET_PAGES":return{...e,pages:t.pages};case"SET_IS_SINGLE_PAGE":return{...e,showSinglePage:t.showSinglePage};case"SET_SINGLE_TEMPLATE":return{...e,singleTemplate:t.singleTemplate};case"SET_PATTERNS":return{...e,patterns:t.patterns};case"SET_TEMPLATES":return{...e,templates:t.templates};case"SET_FAVORITE":return{...e,favorite:t.favorite};case"SET_FILTER":return{...e,filter:t.filter};case"SET_CATEGORIES":return{...e,categories:t.categories};case"SET_IMAGE_IMPORT_TYPE":return{...e,imageImportType:t.imageImportType};case"SET_PATTERNS_PAGE":return{...e,patternsPage:t.patternsPage};case"SET_PAGE_TEMPLATES_PAGE":return{...e,pageTemplatesPage:t.pageTemplatesPage};case"SET_TEMPLATES_PAGE":return{...e,templatesPage:t.templatesPage};case"SET_TEMPLATES_HAS_MORE":return{...e,templatesHasMore:t.templatesHasMore};case"SET_PAGE_HAS_MORE":return{...e,pageHasMore:t.pageHasMore};case"SET_KEY_WORDS":return{...e,keyWords:t.keyWords};default:return e}},z=({children:a})=>{const[r,n]=(0,t.useReducer)(Q,K),l={...r,payload:{version:"3.1.4",auth:"gutenkit",premium:!1}};return(0,e.createElement)(i.Provider,{value:{...l,dispatch:n}},a)};(0,window.wp.plugins.registerPlugin)("gutenkit-template-library",{render:()=>{const a=(0,t.useRef)(null),r=(0,t.useRef)(!0);return(0,t.useEffect)((()=>{if(!r.current)return;const n=(0,d.subscribe)((()=>{const n=document.querySelector(".edit-post-header__toolbar, .editor-header__toolbar, .edit-site-header-edit-mode__start");if(!n)return;let l=document.getElementById("gutenkit-template-library");l||(l=document.createElement("div"),l.id="gutenkit-template-library",n.appendChild(l)),!a.current&&l&&(a.current=(0,t.createRoot)(l)),a.current&&(a.current.render((0,e.createElement)(z,null,(0,e.createElement)(q,null))),r.current=!1)}));return()=>n()}),[]),null}})})()})();