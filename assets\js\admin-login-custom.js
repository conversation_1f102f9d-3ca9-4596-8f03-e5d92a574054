"use strict";function xs_show_hide(e){document.getElementById("xs_data_tr__"+e).classList.toggle("active_tr")}function xs_show_hide_role(e){var t=document.getElementById("xs_data_tr__role");1==e?t.classList.add("active_tr"):t.classList.remove("active_tr")}function copy_callback(e){document.getElementById(e+"_callback").select(),document.execCommand("copy")}function xs_counter_open(e){let t=e.getAttribute("xs-target-id");if(!t)return!1;let o=document.getElementById(t),n=document.querySelector(".xs-backdrop");o&&(o.classList.toggle("is-open"),n.classList.toggle("is-open"))}function social_counter_enable(e){var t=e.getAttribute("data-key"),o=e.checked?1:"",n=e.checked?"Settings":"Getting Started",r=jQuery(e).closest("div.xs-block-footer").find("div.right-content a"),c={val:o,social:t};jQuery.ajax({data:c,type:"post",url:window.rest_api_conf.root+"wp_social/v1/counter/enable/"+t,beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",window.rest_api_conf.nonce),r.text("Waiting...")},success:function(t){t.success?r.text(n):jQuery(e).closest("div").append('<span style="color:red">Unexpected error, please reload the page and retry</span>')},error:function(e){r.text("Error!!!")}})}function social_share_enable(e){var t=e.getAttribute("data-key"),o=e.checked?1:"",n=e.checked?"Settings":"Getting Started",r=jQuery(e).closest("div.xs-block-footer").find("div.right-content a"),c={val:o,social:t};jQuery.ajax({data:c,type:"post",url:window.rest_api_conf.root+"wp_social/v1/share/enable/"+t.replace("-",""),beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",window.rest_api_conf.nonce),r.text("Waiting...")},success:function(t){t.success?r.text(n):jQuery(e).closest("div").append('<span style="color:red">Unexpected error, please reload the page and retry</span>')},error:function(e){r.text("Error!!!")}})}function enable_provider_login(e){var t=e.getAttribute("data-key"),o=e.checked?1:"",n=e.checked?"Settings":"Getting Started",r=jQuery(e).closest("div.xs-block-footer").find("div.right-content a"),c={val:o,social:t};console.log(c,r),jQuery.ajax({data:c,type:"post",url:window.rest_api_conf.root+"wp_social/v1/login/enable/"+t,beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",window.rest_api_conf.nonce),r.text("Waiting...")},success:function(t){t.success?r.text(n):jQuery(e).closest("div").append('<span style="color:red">Unexpected error, please reload the page and retry</span>')},error:function(e){r.text("Error!!!")}})}