=== Wp Social Login and Register Social Counter ===
Contributors: XpeedStudio, Ataurr, emranio
Tags: Social login, WordPress Social login and register, Social share,  Social counter, Social,  WooCommerce social login and register
Requires at least: 5.0
Tested up to: 6.8
Stable tag: 3.1.2
Requires PHP: 7.4
License: GPLv3
License URI: https://www.gnu.org/licenses/gpl-3.0.txt

Wp social lets you add social login, social counter, and social share buttons of different styles to your WordPress website.

== Description ==

> **[BUY PRO](https://wpmet.com/plugin/wp-social/pricing/)** | **[All Features](https://wpmet.com/plugin/wp-social/)** | **[Support](https://help.wpmet.com/)** | **[Docs](https://help.wpmet.com/docs/)** | **[Video Tutorials](https://www.youtube.com/c/Wpmet/videos)** | **[Request a Feature](https://wpmet.com/plugin/wp-social/roadmaps#ideas)**

[Wp Social](https://wpmet.com/plugin/wp-social) lets you add social login, social counter, and social share buttons of different styles to your WordPress website.

A website without traffic is lame, but a website that hates social media is doomed to extinction (Yeah, a viral fever has spread all over, but the world craves for it!)

You might feel decision fatigue to find 3 individual plugins for social login, social counter, and social share to keep your website paced with social media.

Well, why exhaust your website with multiple plugins when only one robust social media plugin can do what you want?

Welcome to WP Social.

Leverage WP Social to make the login process and content sharing of your website easier along with counting the fans and followers of your social media channels.

WP Social is an advanced social media plugin with social login, social counter, and social share features. It brings all your social media channels together without much ado!

This amazing plugin allows your visitors to login to your site using their social accounts and share your content on different social media platforms. You can also display the number of your fans and followers along with linking the icons to your social channels.

Unleash the power of social media integration features that you can configure effortlessly from the WordPress admin panel. You can also enable/disable any features of it to make sure you implement what's important for you.

##Social Register/Login##

Social register/login buttons will make the login process for your visitors easy and simple. With social login, your website visitors can register and log in to your site through dozens of popular social networks seamlessly.

As a website owner, you can manage custom login redirect URL, show or hide social login buttons for different pages including wp-login, wp register, comment, WooCommerce login, WooCommerce register, and much more.

WP Social integrates 9 social login providers that’ll allow your visitors login to your website within seconds via Facebook, Google, Twitter, LinkedIn, Dribbble, GitHub, WordPress, Vkontakte, and Reddit.

Wp Social Login features WordPress Social login and register, Woocommerce Social login and register, BuddyPress Social login and register, Facebook login, Twitter login, Linkedin login, Dribble login, Pinterest login, WordPress login, and Instagram login from your WordPress site.

The social login offers a bunch of beautifully crafted readymade designs that will give your login button an extra classy look.

Just put the login authorization details in the admin panel and you are ready to use this plugin on your site.

##Social Share##

Do you want your visitors to share your web content to the social media platforms just by a click on the social media icons? Leverage the social share feature from WP Social to get your work done.

With the social share feature, your visitors can easily share the post or content from your website to any of the 15 most popular social platforms like Facebook, Twitter, and Pinterest.

This set of social share buttons will help your users directly share anything with their friends and followers of respective social media.

When it comes to styling, you can choose from readymade styles for displaying the share buttons on your website. On top of that, you can determine the layout either as horizontal or vertical.

And last but not least, you are at complete liberty to show or hide the share count to your visitors.

##Social Counter##

Well, now is the time to introduce you to the social counter buttons.

Do you prefer to showcase the number of social media fans, subscribers, and followers to the visitors of your website? No worries, we have integrated a social counter feature in WP Social for you.

This feature will give you the means to count your social media fans and display the numbers to the visitors.

With this feature, you can impress your potential followers by showing the number of subscribers, fans, and followers you have earned with 15 providers including Facebook, Twitter, YouTube, and LinkedIn.

Like others, this feature also offers extensive options to select from 15+ hover effects and icon styles such as flat icons, rounded icons, hover effect, color effect, metro style, etc.

We have extensive options to activate this feature with your social media details and select one of the readymade designs from beautifully designed templates.

You can use either widget or shortcode to display your social media counter and shares right on your website in your desired locations!

You can determine the cache hours to store the counter data for a certain period of time.

In all the features mentioned above, WP Social lets you use simple shortcodes anywhere on your site. You do not need knowledge of technical and complicated lengthy code anymore.

##🔥 Top Features of Wp Social##

**👉🏻Add Social Register**
**👉🏻Enable Social Count**
**👉🏻Integrate Social Share**
**👉🏻Add Social Login**
**👉🏻Count Posts**
**👉🏻Count Comments**
**👉🏻Use Shortcodes to Show Buttons on Any Page**
**👉🏻Easy to Set-up and Use**
**👉🏻Up to date API**
**👉🏻GDPR Compliant**
**👉🏻Choose your Favorite Font Family**
**👉🏻Enable/Disable Features**
**👉🏻Customizable Caching Hours**
**👉🏻Unlimited Customizations**
**👉🏻Optimized for Swift Performance**
**👉🏻14+ Social Share Providers**
**👉🏻8+ Social Counter Providers**
**👉🏻8+ Social Login Providers**
**👉🏻Customize Login and Logout Redirect URL.**
**👉🏻Customizable Text For Social Login Icons.**
**👉🏻One-click registration and Social Login via Woocommerce, Buddypress, Facebook, Twitter, Linkedin, Dribble, Pinterest, Instagram, Reddit, Vkontakte**
**👉🏻Set Up Your Own Social Login Application with APP ID and APP Secret for Facebook, Twitter.**
**👉🏻Editable And Translatable Texts on the Login Buttons.**



If you are a blogger, influencer, webmaster, or social media manager looking for easier social media integration to the website, Wp Social is for you.


##🚀 It's Time to Get Started with Wp Social##

How to install and use Wp Social (video embed)

##🚀 Backed By a Trusted Team ##

Wp Social is brought to you by Wpmet, a name trusted by 3,00,000+ satisfied users worldwide.

## 👨‍💻 DOCUMENTATION AND SUPPORT ##


- 👨‍💻 [Contact Our Support](https://wpmet.com/support-ticket-form/)
- 👫 [Join Our Facebook Community](https://www.facebook.com/groups/wpmet/)
- 📜 [Check Documentation](https://help.wpmet.com/docs/)
- 📝 [Browse Changelogs](https://wpmet.com/plugin/wp-social/roadmaps/#updates)

##💙 Loved Wp Social?##

🔸Stay Updated with our [Youtube Channel](https://www.youtube.com/c/Wpmet/videos)

🔸Rate us on [WordPress](https://wordpress.org/support/plugin/******************)

##🔥 What’s Next?##

If you like using Wp Social, then consider checking out our other plugins:

[ShopEngine](https://wpmet.com/plugin/shopengine/) – Leverage ShopEngine WooCommerce Builder for Elementor to build your eCommerce website from scratch.

[ElementsKit](https://wpmet.com/plugin/elementskit/) – All-in-one Addons for Elementor featuring 70+ widgets and modules.

[MetForm](https://wpmet.com/plugin/metform/) – The most flexible and easy-to-use form builder.

[Wp Fundraising](https://products.wpmet.com/crowdfunding/) – Employ the power of Wp Fundraising to create a crowdfunding and donation site with WordPress.

[Wp Ultimate Review](https://products.wpmet.com/review/) – Manage customer reviews with Wp Ultimate Review plugin.

Visit [wpmet](https://wpmet.com) to learn more about how to get the best of WordPress with [Tutorial, Tips & Tricks](https://wpmet.com/blog)!


== Screenshots ==
1. Global settings
2. Social Settings
3. Social Style 1
4. Social Style 2
5. Social Style 3
6. App Doc
7. App id and secret key
8. App button style
9. Additional way



== Changelog ==
Version 3.1.2 // 2025-04-16
Fixed: Compatibility issue with WordPress 6.8.

Version 3.1.1 // 2025-02-24
Fixed: XSS vulnerability in the Social Counter.

Version 3.1.0 // 2025-02-09
Fixed: Rest api base url issue.
Fixed: Show button to wp-login page switch enable/disable issue.

Version 3.0.9 // 2024-12-02
Fixed: Youtube subscribers counter redirect URL issue
Fixed: Twitter followers counter issue
Fixed: Social counter configuration settings modal issue

Version 3.0.8 // 2024-10-20
Improved: Social login security.

Version 3.0.7 // 2024-10-07
Fixed: Login with Vkontakte is not working.
Fixed: Profile photo is re-uploading after social login.

Version 3.0.6 // 2024-09-17
-Fixed: Minimum PHP required version issue.

Version 3.0.5 // 2024-09-15
-Updated: Hybridauth and phpseclib libraries.

Version 3.0.4 // 2024-07-25
-Fixed: Warning notice on block editor.

Version 3.0.3 // 2024-06-23
-Added: Option to disable promotional content.
-Improved: Admin UI.

Version 3.0.2 // 2024-05-13
-Added: Action hooks before social login user creation.
-Improved: Admin UI.
-Fixed: X (Twitter) logo issue.

Version 3.0.1 // 2024-02-27
-Fixed: Conflict with Advanced Custom Fields (ACF) plugin.
-Fixed: Enable provider vulnerability.
-Fixed: Not going to the accurate account while click on the Pinterest icon.

Version 3.0.0 // 2023-12-26
-New: LinkedInOpenId support

Version 2.2.9 // 2023-11-27
-Fixed: Login provider ordering issue

Version 2.2.8 // 2023-11-21
-Fixed: Pinterest counter not working
-Fixed: Sign In with Linkedin issue

Version 2.2.6 // 2023-09-19
-Improved: User experience

Version 2.2.5 // 2023-09-14
-Fixed: Line app login issue.
-Fixed: Login redirect is not working with the Line App.

Version 2.2.4 // 2023-09-04
-Fixed: Admin notice display for not admin users issue.

Version 2.2.3 // 2023-06-04
-Fixed: Share settings not working for single page and post page

Version 2.2.2 // 2023-03-09
-Fixed: Some translation issues.
-Fixed: Share label and default share counter.

Version 2.2.1 // 2022-12-12
-Fixed: Facebook Messenger sharing is not working
-Fixed: The user profile image has been synced (while login) even it is disabled from setting
-Fixed: Instagram not redirecting to the right URL for counter

Version 2.2.0 // 2022-11-15
- Fixed : Provider not saving issue at backend
- Fixed : Share, counter, login widgets design issue
– Compatibility: compatible with WordPress 6.1

Version 2.1.0 // 2022-11-10
- Improved: Security

Version 2.0.1 // 2022-11-01
- Improved: Coding standard

Version 2.0.0 // 2022-10-30
- Improved: Security
- Improved: CSS & JS

Version 1.9.0 // 2022-08-31
- Fixed: Facebook counter issue
- Fixed: Counter providers ordering issue
- Fixed: Social counter showing wrong text issue
- Fixed: Login link visible issue on hover
- Tweaked: Security improved
- Compatibility: compatible with Wordpress 6.0

Version 1.8.6 // 2022-04-19
- Tweaked: Global Settings.
- Tweaked: Minor issues reguarding no-follow attribute.
- Tweaked: JS & CSS. 

Version 1.8.5 // 2022-03-29
- Added: Oxizen builder compatibility support.
- Fixed: Some minor issues.
- Tweaked: CSS and JS improved.

Version 1.8.4 // 2022-01-22
-Improved: Rating asking alert for better user experience.
-Tweaked: CSS and JS improved.

Version 1.8.3 // 2022-01-09
-Fixed: Share widget responsive issue
-Fixed: Mailchimp counter cached time issue
-Fixed: Missing dependency issue
-Tweaked: CSS and JS improved

Version 1.8.2 // 2021-11-23
- Fixed: onboard screen conflict with other wpmet plugin
- Tweaked: CSS and JS improved

Version 1.8.1 // 2021-11-14
- Fixed: onboard screen does not get completed

Version 1.8.0 // 2021-11-10
- added: Added: Onboard Integration
- fixed: Share button widget style not changing issue
- Tweaked: CSS and JS improved

Version 1.7.4 // 2021-08-10
- fix: Provider select does not work from widget issue is resolved
- fix: LINE app email conflict is resolved
- fix: PHP 8 notices is resolved
- Tweak: CSS and JS improved
- Compatibility: compatible with Wordpress 5.8
- Compatibility: compatible with PHP 8

Version 1.7.3 // 2021-07-19
- fix: Some minor issues
- Tweak: CSS and JS improved


Version 1.7.2 // 2021-07-15
- fix: Error is shown when try to login with social provider except LINE app


Version 1.7.1 // 2021-07-12
- Add: New login provider added "LINE" app
- Add: New option added 'Email new registered user'
- fix: Bold tag in login button issue is resolved
- fix: Global css issue
- Fix: Some minor issues
- Tweak: Icon updated
- Tweak: CSS and JS improved
- Required PHP version updated to 7.0


Version 1.7.0 // 2021-05-19
- Add: Drag & drop to sort the providers
- Add: Manual cache clear mechanism in social counter
- Add: Suggestion for custom login redirect implemented
- Fix: User reauth issue after deleting the user
- Fix: Linkedin login issue is resolved
- Fix: Dismiss button does not work issue
- Fix: Some minor issues
- Tweak: CSS and JS improved
- Compatibility: compatible with Wordpress 5.7.2


Version 1.6.1 // 2021-02-03
- Fix: Headers already sent warning issue is fixed


Version 1.6.0
- Add: New option added for individual post/page to override the global social share button appearance
- Tweak: style parameter added for login shortcode
- Tweak: social share style settings page layout re-designed
- Fix: Counter not showing from API call issue is fixed
- Fix: Documentation & support links are updated
- Tweak: CSS and JS improved


Version 1.5.0
- Add: License key activation field added
- Fix: Provider is not coming in widget dropdown
- Fix: Some spelling corrected
- Fix: Notice on elementor editor issue
- Tweak: CSS and JS improved


Version 1.4.9
- Fix: Some minor bug fix
- Fix: Some spelling corrected
- Tweak: CSS and JS improved
- Compatibility: Compatible with WordPress 5.6


Version 1.4.8
- Fix: Conflict with brizy editor issue resolved
- Fix: Some other minor bug fix
- Tweak: CSS and JS improved


Version 1.4.7
Fix: Social share content is not appending or prepending issue is resolved
- Fix: Some other minor bug fix
- Tweak: Code improved
- Tweak: CSS and JS improved


Version 1.4.6
- Fix: Instagram count checking request will be fired only if the Instagram count is turned on
- Tweak: Social login providers activation button redesigned


Version 1.4.5
- Fix: Some php notice resolved for new installation


Version 1.4.4
- Fix: Style dropdown values are not coming in elementor editor for share widget
- Fix: Pinterest actual count is not showing in social counter issue is resolved
- Fix: Elementor editor is not loading issue is resolved
- Fix: Shortcode is not working issue is resolved
- Tweak: Improved youtube social count
- Tweak: Improved dribbble social count
- Tweak: Improved twitter social count
- Tweak: Social share providers activation button redesigned
- Tweak: Social count providers activation button redesigned
- Tweak: Post & comments counts shows actual count if default count is not given in social count
- Tweak: Global cache time now can be decimal too
- Tweak: Code improved
- Tweak: CSS and JS improved


Version 1.4.3
- Fix: WordPress compatibility issues


Version 1.4.2
- Fix: WordPress compatibility issues
- Tweak: change the default value priority with actual values


Version 1.4.1
- Fix: app creation doc updated
- Fix: Instagram social counter not working issue
- Tweak: CSS and JS improved


Version 1.4.0
- fix: user data issues
- Tweak: Improved design


Version 1.3.11
- Tweak: username generation logic updated. Instead of a unique identifier sent from the social provider, now username will be first_name+last_name and all space removed and lower-cased.


Version 1.3.10
- Fix: A fatal error occurring in some environment


Version 1.3.9
- Fix: Default role issue fixed
- Fix: A fatal error due to conflict
- Add: Sending email notification to admin and users on registration


Version 1.3.8
- Fix: Page redirection issue with custom url
- Fix: Page redirection issue with default redirect_to param
- Fix: A fatal error conflict with another plugin
- Fix: Some minor PHP notices
- Tweak: Social media profile picture
- Tweak: Username generation from social media identifier
- Tweak: Improved login security


Version 1.3.7
Login restriction without password

Version 1.3.6
Fixed login security issue

Version 1.3.5
Fixed Facebook login issue
Fixed #hover style name appearing in widget for counter and share
Fixed google login
Tweaks

Version 1.3.3
Counter issue fixed

Version 1.3.2
fixed Woocommerce edit issue
Fixed Counter widget

Version 1.3.1
Route issue fixed


Version  1.3.0
Fixed Mac backslash issue

Version  1.2.9
Translation issue fixed

Version1.2.8
Share button issue fixed

Version 1.2.7
Admin UI updated

= 1.0.10 =
Donate form second style checkout
Fixed Reward title field type

* initial release

== Upgrade Notice ==
WordPress 4.9+


== Installation ==


**Installation Process->1:** Go to the WordPress Dashboard➔Plugins➔Add New➔Search for “Wp Social”➔Install and Activate it.

**OR**

**Step->1:** Unzip the Wp Social Folder if it is Zipped➔Upload it to the /wp-content/plugins/plugin-name directory.

**Step->2:** After successful installation, just go to Installed Plugins➔click on the “activate” button to activate Wp Social.

**Step->3:** All Settings will be found in the Wp Social menu.

For further details check out the following [documentation](https://help.wpmet.com/docs/).

== Frequently Asked Questions ==

= How to use WP Social login? =

Login to your WordPress dashboard,  From the left menu, click on Wp social icon to customize the Settings.

= How can I report security bugs? = 

You can report security bugs through the Patchstack Vulnerability Disclosure Program. The Patchstack team help validate, triage and handle any security vulnerabilities. [Report a security vulnerability.](https://patchstack.com/database/vdp/wp-social)

