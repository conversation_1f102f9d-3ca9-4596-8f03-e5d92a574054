/*! Sortable 1.13.0 - MIT | git://github.com/SortableJS/Sortable.git */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).Sortable=e()}(this,(function(){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function e(){return(e=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}function n(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},o=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),o.forEach((function(e){var o,i,r;o=t,r=n[i=e],i in o?Object.defineProperty(o,i,{value:r,enumerable:!0,configurable:!0,writable:!0}):o[i]=r}))}return t}function o(t,e){if(null==t)return{};var n,o,i=function(t,e){if(null==t)return{};var n,o,i={},r=Object.keys(t);for(o=0;o<r.length;o++)n=r[o],0<=e.indexOf(n)||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(o=0;o<r.length;o++)n=r[o],0<=e.indexOf(n)||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}function i(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function r(t){if("undefined"!=typeof window&&window.navigator)return!!navigator.userAgent.match(t)}var a=r(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),l=r(/Edge/i),s=r(/firefox/i),c=r(/safari/i)&&!r(/chrome/i)&&!r(/android/i),u=r(/iP(ad|od|hone)/i),d=r(/chrome/i)&&r(/android/i),h={capture:!1,passive:!1};function f(t,e,n){t.addEventListener(e,n,!a&&h)}function p(t,e,n){t.removeEventListener(e,n,!a&&h)}function g(t,e){if(e){if(">"===e[0]&&(e=e.substring(1)),t)try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch(t){return!1}return!1}}function v(t,e,n,o){if(t){n=n||document;do{if(null!=e&&(">"===e[0]?t.parentNode===n&&g(t,e):g(t,e))||o&&t===n)return t;if(t===n)break}while(t=(i=t).host&&i!==document&&i.host.nodeType?i.host:i.parentNode)}var i;return null}var m,b=/\s+/g;function y(t,e,n){if(t&&e)if(t.classList)t.classList[n?"add":"remove"](e);else{var o=(" "+t.className+" ").replace(b," ").replace(" "+e+" "," ");t.className=(o+(n?" "+e:"")).replace(b," ")}}function w(t,e,n){var o=t&&t.style;if(o){if(void 0===n)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(n=t.currentStyle),void 0===e?n:n[e];e in o||-1!==e.indexOf("webkit")||(e="-webkit-"+e),o[e]=n+("string"==typeof n?"":"px")}}function E(t,e){var n="";if("string"==typeof t)n=t;else do{var o=w(t,"transform");o&&"none"!==o&&(n=o+" "+n)}while(!e&&(t=t.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(n)}function D(t,e,n){if(t){var o=t.getElementsByTagName(e),i=0,r=o.length;if(n)for(;i<r;i++)n(o[i],i);return o}return[]}function S(){return document.scrollingElement||document.documentElement}function _(t,e,n,o,i){if(t.getBoundingClientRect||t===window){var r,l,s,c,u,d,h;if(h=t!==window&&t.parentNode&&t!==S()?(l=(r=t.getBoundingClientRect()).top,s=r.left,c=r.bottom,u=r.right,d=r.height,r.width):(s=l=0,c=window.innerHeight,u=window.innerWidth,d=window.innerHeight,window.innerWidth),(e||n)&&t!==window&&(i=i||t.parentNode,!a))do{if(i&&i.getBoundingClientRect&&("none"!==w(i,"transform")||n&&"static"!==w(i,"position"))){var f=i.getBoundingClientRect();l-=f.top+parseInt(w(i,"border-top-width")),s-=f.left+parseInt(w(i,"border-left-width")),c=l+r.height,u=s+r.width;break}}while(i=i.parentNode);if(o&&t!==window){var p=E(i||t),g=p&&p.a,v=p&&p.d;p&&(c=(l/=v)+(d/=v),u=(s/=g)+(h/=g))}return{top:l,left:s,bottom:c,right:u,width:h,height:d}}}function C(t,e,n){for(var o=N(t,!0),i=_(t)[e];o;){var r=_(o)[n];if(!("top"===n||"left"===n?r<=i:i<=r))return o;if(o===S())break;o=N(o,!1)}return!1}function T(t,e,n){for(var o=0,i=0,r=t.children;i<r.length;){if("none"!==r[i].style.display&&r[i]!==kt.ghost&&r[i]!==kt.dragged&&v(r[i],n.draggable,t,!1)){if(o===e)return r[i];o++}i++}return null}function x(t,e){for(var n=t.lastElementChild;n&&(n===kt.ghost||"none"===w(n,"display")||e&&!g(n,e));)n=n.previousElementSibling;return n||null}function M(t,e){var n=0;if(!t||!t.parentNode)return-1;for(;t=t.previousElementSibling;)"TEMPLATE"===t.nodeName.toUpperCase()||t===kt.clone||e&&!g(t,e)||n++;return n}function O(t){var e=0,n=0,o=S();if(t)do{var i=E(t),r=i.a,a=i.d;e+=t.scrollLeft*r,n+=t.scrollTop*a}while(t!==o&&(t=t.parentNode));return[e,n]}function N(t,e){if(!t||!t.getBoundingClientRect)return S();var n=t,o=!1;do{if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var i=w(n);if(n.clientWidth<n.scrollWidth&&("auto"==i.overflowX||"scroll"==i.overflowX)||n.clientHeight<n.scrollHeight&&("auto"==i.overflowY||"scroll"==i.overflowY)){if(!n.getBoundingClientRect||n===document.body)return S();if(o||e)return n;o=!0}}}while(n=n.parentNode);return S()}function A(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}function I(t,e){return function(){if(!m){var n=arguments;1===n.length?t.call(this,n[0]):t.apply(this,n),m=setTimeout((function(){m=void 0}),e)}}}function P(t,e,n){t.scrollLeft+=e,t.scrollTop+=n}function k(t){var e=window.Polymer,n=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):n?n(t).clone(!0)[0]:t.cloneNode(!0)}function R(t,e){w(t,"position","absolute"),w(t,"top",e.top),w(t,"left",e.left),w(t,"width",e.width),w(t,"height",e.height)}function X(t){w(t,"position",""),w(t,"top",""),w(t,"left",""),w(t,"width",""),w(t,"height","")}var Y="Sortable"+(new Date).getTime();var B=[],F={initializeByDefault:!0},H={mount:function(t){for(var e in F)!F.hasOwnProperty(e)||e in t||(t[e]=F[e]);B.forEach((function(e){if(e.pluginName===t.pluginName)throw"Sortable: Cannot mount plugin ".concat(t.pluginName," more than once")})),B.push(t)},pluginEvent:function(t,e,o){var i=this;this.eventCanceled=!1,o.cancel=function(){i.eventCanceled=!0};var r=t+"Global";B.forEach((function(i){e[i.pluginName]&&(e[i.pluginName][r]&&e[i.pluginName][r](n({sortable:e},o)),e.options[i.pluginName]&&e[i.pluginName][t]&&e[i.pluginName][t](n({sortable:e},o)))}))},initializePlugins:function(t,n,o,i){for(var r in B.forEach((function(i){var r=i.pluginName;if(t.options[r]||i.initializeByDefault){var a=new i(t,n,t.options);a.sortable=t,a.options=t.options,t[r]=a,e(o,a.defaults)}})),t.options)if(t.options.hasOwnProperty(r)){var a=this.modifyOption(t,r,t.options[r]);void 0!==a&&(t.options[r]=a)}},getEventProperties:function(t,n){var o={};return B.forEach((function(i){"function"==typeof i.eventProperties&&e(o,i.eventProperties.call(n[i.pluginName],t))})),o},modifyOption:function(t,e,n){var o;return B.forEach((function(i){t[i.pluginName]&&i.optionListeners&&"function"==typeof i.optionListeners[e]&&(o=i.optionListeners[e].call(t[i.pluginName],n))})),o}};function L(t){var e=t.sortable,o=t.rootEl,i=t.name,r=t.targetEl,s=t.cloneEl,c=t.toEl,u=t.fromEl,d=t.oldIndex,h=t.newIndex,f=t.oldDraggableIndex,p=t.newDraggableIndex,g=t.originalEvent,v=t.putSortable,m=t.extraEventProperties;if(e=e||o&&o[Y]){var b,y=e.options,w="on"+i.charAt(0).toUpperCase()+i.substr(1);!window.CustomEvent||a||l?(b=document.createEvent("Event")).initEvent(i,!0,!0):b=new CustomEvent(i,{bubbles:!0,cancelable:!0}),b.to=c||o,b.from=u||o,b.item=r||o,b.clone=s,b.oldIndex=d,b.newIndex=h,b.oldDraggableIndex=f,b.newDraggableIndex=p,b.originalEvent=g,b.pullMode=v?v.lastPutMode:void 0;var E=n({},m,H.getEventProperties(i,e));for(var D in E)b[D]=E[D];o&&o.dispatchEvent(b),y[w]&&y[w].call(e,b)}}function j(t,e,i){var r=2<arguments.length&&void 0!==i?i:{},a=r.evt,l=o(r,["evt"]);H.pluginEvent.bind(kt)(t,e,n({dragEl:W,parentEl:z,ghostEl:G,rootEl:U,nextEl:q,lastDownEl:V,cloneEl:Z,cloneHidden:Q,dragStarted:ut,putSortable:ot,activeSortable:kt.active,originalEvent:a,oldIndex:$,oldDraggableIndex:tt,newIndex:J,newDraggableIndex:et,hideGhostForTarget:Nt,unhideGhostForTarget:At,cloneNowHidden:function(){Q=!0},cloneNowShown:function(){Q=!1},dispatchSortableEvent:function(t){K({sortable:e,name:t,originalEvent:a})}},l))}function K(t){L(n({putSortable:ot,cloneEl:Z,targetEl:W,rootEl:U,oldIndex:$,oldDraggableIndex:tt,newIndex:J,newDraggableIndex:et},t))}var W,z,G,U,q,V,Z,Q,$,J,tt,et,nt,ot,it,rt,at,lt,st,ct,ut,dt,ht,ft,pt,gt=!1,vt=!1,mt=[],bt=!1,yt=!1,wt=[],Et=!1,Dt=[],St="undefined"!=typeof document,_t=u,Ct=l||a?"cssFloat":"float",Tt=St&&!d&&!u&&"draggable"in document.createElement("div"),xt=function(){if(St){if(a)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto","auto"===t.style.pointerEvents}}(),Mt=function(t,e){var n=w(t),o=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),i=T(t,0,e),r=T(t,1,e),a=i&&w(i),l=r&&w(r),s=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+_(i).width,c=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+_(r).width;if("flex"===n.display)return"column"===n.flexDirection||"column-reverse"===n.flexDirection?"vertical":"horizontal";if("grid"===n.display)return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&a.float&&"none"!==a.float){var u="left"===a.float?"left":"right";return!r||"both"!==l.clear&&l.clear!==u?"horizontal":"vertical"}return i&&("block"===a.display||"flex"===a.display||"table"===a.display||"grid"===a.display||o<=s&&"none"===n[Ct]||r&&"none"===n[Ct]&&o<s+c)?"vertical":"horizontal"},Ot=function(e){function n(t,e){return function(o,i,r,a){var l=o.options.group.name&&i.options.group.name&&o.options.group.name===i.options.group.name;if(null==t&&(e||l))return!0;if(null==t||!1===t)return!1;if(e&&"clone"===t)return t;if("function"==typeof t)return n(t(o,i,r,a),e)(o,i,r,a);var s=(e?o:i).options.group.name;return!0===t||"string"==typeof t&&t===s||t.join&&-1<t.indexOf(s)}}var o={},i=e.group;i&&"object"==t(i)||(i={name:i}),o.name=i.name,o.checkPull=n(i.pull,!0),o.checkPut=n(i.put),o.revertClone=i.revertClone,e.group=o},Nt=function(){!xt&&G&&w(G,"display","none")},At=function(){!xt&&G&&w(G,"display","")};function It(t){if(W){var e=function(t,e){var n;return mt.some((function(o){if(!x(o)){var i=_(o),r=o[Y].options.emptyInsertThreshold,a=t>=i.left-r&&t<=i.right+r,l=e>=i.top-r&&e<=i.bottom+r;return r&&a&&l?n=o:void 0}})),n}((t=t.touches?t.touches[0]:t).clientX,t.clientY);if(e){var n={};for(var o in t)t.hasOwnProperty(o)&&(n[o]=t[o]);n.target=n.rootEl=e,n.preventDefault=void 0,n.stopPropagation=void 0,e[Y]._onDragOver(n)}}}function Pt(t){W&&W.parentNode[Y]._isOutsideThisEl(t.target)}function kt(t,o){if(!t||!t.nodeType||1!==t.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=o=e({},o),t[Y]=this;var i={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Mt(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(t,e){t.setData("Text",e.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==kt.supportPointer&&"PointerEvent"in window&&!c,emptyInsertThreshold:5};for(var r in H.initializePlugins(this,t,i),i)r in o||(o[r]=i[r]);for(var a in Ot(o),this)"_"===a.charAt(0)&&"function"==typeof this[a]&&(this[a]=this[a].bind(this));this.nativeDraggable=!o.forceFallback&&Tt,this.nativeDraggable&&(this.options.touchStartThreshold=1),o.supportPointer?f(t,"pointerdown",this._onTapStart):(f(t,"mousedown",this._onTapStart),f(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(f(t,"dragover",this),f(t,"dragenter",this)),mt.push(this.el),o.store&&o.store.get&&this.sort(o.store.get(this)||[]),e(this,function(){var t,e=[];return{captureAnimationState:function(){e=[],this.options.animation&&[].slice.call(this.el.children).forEach((function(t){if("none"!==w(t,"display")&&t!==kt.ghost){e.push({target:t,rect:_(t)});var o=n({},e[e.length-1].rect);if(t.thisAnimationDuration){var i=E(t,!0);i&&(o.top-=i.f,o.left-=i.e)}t.fromRect=o}}))},addAnimationState:function(t){e.push(t)},removeAnimationState:function(t){e.splice(function(t,e){for(var n in t)if(t.hasOwnProperty(n))for(var o in e)if(e.hasOwnProperty(o)&&e[o]===t[n][o])return Number(n);return-1}(e,{target:t}),1)},animateAll:function(n){var o=this;if(!this.options.animation)return clearTimeout(t),void("function"==typeof n&&n());var i=!1,r=0;e.forEach((function(t){var e=0,n=t.target,a=n.fromRect,l=_(n),s=n.prevFromRect,c=n.prevToRect,u=t.rect,d=E(n,!0);d&&(l.top-=d.f,l.left-=d.e),n.toRect=l,n.thisAnimationDuration&&A(s,l)&&!A(a,l)&&(u.top-l.top)/(u.left-l.left)==(a.top-l.top)/(a.left-l.left)&&(e=function(t,e,n,o){return Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))/Math.sqrt(Math.pow(e.top-n.top,2)+Math.pow(e.left-n.left,2))*o.animation}(u,s,c,o.options)),A(l,a)||(n.prevFromRect=a,n.prevToRect=l,e||(e=o.options.animation),o.animate(n,u,l,e)),e&&(i=!0,r=Math.max(r,e),clearTimeout(n.animationResetTimer),n.animationResetTimer=setTimeout((function(){n.animationTime=0,n.prevFromRect=null,n.fromRect=null,n.prevToRect=null,n.thisAnimationDuration=null}),e),n.thisAnimationDuration=e)})),clearTimeout(t),i?t=setTimeout((function(){"function"==typeof n&&n()}),r):"function"==typeof n&&n(),e=[]},animate:function(t,e,n,o){if(o){w(t,"transition",""),w(t,"transform","");var i=E(this.el),r=i&&i.a,a=i&&i.d,l=(e.left-n.left)/(r||1),s=(e.top-n.top)/(a||1);t.animatingX=!!l,t.animatingY=!!s,w(t,"transform","translate3d("+l+"px,"+s+"px,0)"),this.forRepaintDummy=function(t){return t.offsetWidth}(t),w(t,"transition","transform "+o+"ms"+(this.options.easing?" "+this.options.easing:"")),w(t,"transform","translate3d(0,0,0)"),"number"==typeof t.animated&&clearTimeout(t.animated),t.animated=setTimeout((function(){w(t,"transition",""),w(t,"transform",""),t.animated=!1,t.animatingX=!1,t.animatingY=!1}),o)}}}}())}function Rt(t,e,n,o,i,r,s,c){var u,d,h=t[Y],f=h.options.onMove;return!window.CustomEvent||a||l?(u=document.createEvent("Event")).initEvent("move",!0,!0):u=new CustomEvent("move",{bubbles:!0,cancelable:!0}),u.to=e,u.from=t,u.dragged=n,u.draggedRect=o,u.related=i||e,u.relatedRect=r||_(e),u.willInsertAfter=c,u.originalEvent=s,t.dispatchEvent(u),f&&(d=f.call(h,u,s)),d}function Xt(t){t.draggable=!1}function Yt(){Et=!1}function Bt(t){for(var e=t.tagName+t.className+t.src+t.href+t.textContent,n=e.length,o=0;n--;)o+=e.charCodeAt(n);return o.toString(36)}function Ft(t){return setTimeout(t,0)}function Ht(t){return clearTimeout(t)}St&&document.addEventListener("click",(function(t){if(vt)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),vt=!1}),!0),kt.prototype={constructor:kt,_isOutsideThisEl:function(t){this.el.contains(t)||t===this.el||(dt=null)},_getDirection:function(t,e){return"function"==typeof this.options.direction?this.options.direction.call(this,t,e,W):this.options.direction},_onTapStart:function(t){if(t.cancelable){var e=this,n=this.el,o=this.options,i=o.preventOnFilter,r=t.type,a=t.touches&&t.touches[0]||t.pointerType&&"touch"===t.pointerType&&t,l=(a||t).target,s=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||l,u=o.filter;if(function(t){Dt.length=0;for(var e=t.getElementsByTagName("input"),n=e.length;n--;){var o=e[n];o.checked&&Dt.push(o)}}(n),!W&&!(/mousedown|pointerdown/.test(r)&&0!==t.button||o.disabled)&&!s.isContentEditable&&(this.nativeDraggable||!c||!l||"SELECT"!==l.tagName.toUpperCase())&&!((l=v(l,o.draggable,n,!1))&&l.animated||V===l)){if($=M(l),tt=M(l,o.draggable),"function"==typeof u){if(u.call(this,t,l,this))return K({sortable:e,rootEl:s,name:"filter",targetEl:l,toEl:n,fromEl:n}),j("filter",e,{evt:t}),void(i&&t.cancelable&&t.preventDefault())}else if(u&&(u=u.split(",").some((function(o){if(o=v(s,o.trim(),n,!1))return K({sortable:e,rootEl:o,name:"filter",targetEl:l,fromEl:n,toEl:n}),j("filter",e,{evt:t}),!0}))))return void(i&&t.cancelable&&t.preventDefault());o.handle&&!v(s,o.handle,n,!1)||this._prepareDragStart(t,a,l)}}},_prepareDragStart:function(t,e,n){var o,i=this,r=i.el,c=i.options,u=r.ownerDocument;if(n&&!W&&n.parentNode===r){var d=_(n);if(U=r,z=(W=n).parentNode,q=W.nextSibling,V=n,nt=c.group,it={target:kt.dragged=W,clientX:(e||t).clientX,clientY:(e||t).clientY},st=it.clientX-d.left,ct=it.clientY-d.top,this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,W.style["will-change"]="all",o=function(){j("delayEnded",i,{evt:t}),kt.eventCanceled?i._onDrop():(i._disableDelayedDragEvents(),!s&&i.nativeDraggable&&(W.draggable=!0),i._triggerDragStart(t,e),K({sortable:i,name:"choose",originalEvent:t}),y(W,c.chosenClass,!0))},c.ignore.split(",").forEach((function(t){D(W,t.trim(),Xt)})),f(u,"dragover",It),f(u,"mousemove",It),f(u,"touchmove",It),f(u,"mouseup",i._onDrop),f(u,"touchend",i._onDrop),f(u,"touchcancel",i._onDrop),s&&this.nativeDraggable&&(this.options.touchStartThreshold=4,W.draggable=!0),j("delayStart",this,{evt:t}),!c.delay||c.delayOnTouchOnly&&!e||this.nativeDraggable&&(l||a))o();else{if(kt.eventCanceled)return void this._onDrop();f(u,"mouseup",i._disableDelayedDrag),f(u,"touchend",i._disableDelayedDrag),f(u,"touchcancel",i._disableDelayedDrag),f(u,"mousemove",i._delayedDragTouchMoveHandler),f(u,"touchmove",i._delayedDragTouchMoveHandler),c.supportPointer&&f(u,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(o,c.delay)}}},_delayedDragTouchMoveHandler:function(t){var e=t.touches?t.touches[0]:t;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){W&&Xt(W),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;p(t,"mouseup",this._disableDelayedDrag),p(t,"touchend",this._disableDelayedDrag),p(t,"touchcancel",this._disableDelayedDrag),p(t,"mousemove",this._delayedDragTouchMoveHandler),p(t,"touchmove",this._delayedDragTouchMoveHandler),p(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||"touch"==t.pointerType&&t,!this.nativeDraggable||e?this.options.supportPointer?f(document,"pointermove",this._onTouchMove):f(document,e?"touchmove":"mousemove",this._onTouchMove):(f(W,"dragend",this),f(U,"dragstart",this._onDragStart));try{document.selection?Ft((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(t){}},_dragStarted:function(t,e){if(gt=!1,U&&W){j("dragStarted",this,{evt:e}),this.nativeDraggable&&f(document,"dragover",Pt);var n=this.options;t||y(W,n.dragClass,!1),y(W,n.ghostClass,!0),kt.active=this,t&&this._appendGhost(),K({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(rt){this._lastX=rt.clientX,this._lastY=rt.clientY,Nt();for(var t=document.elementFromPoint(rt.clientX,rt.clientY),e=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(rt.clientX,rt.clientY))!==e;)e=t;if(W.parentNode[Y]._isOutsideThisEl(t),e)do{if(e[Y]&&e[Y]._onDragOver({clientX:rt.clientX,clientY:rt.clientY,target:t,rootEl:e})&&!this.options.dragoverBubble)break;t=e}while(e=e.parentNode);At()}},_onTouchMove:function(t){if(it){var e=this.options,n=e.fallbackTolerance,o=e.fallbackOffset,i=t.touches?t.touches[0]:t,r=G&&E(G,!0),a=G&&r&&r.a,l=G&&r&&r.d,s=_t&&pt&&O(pt),c=(i.clientX-it.clientX+o.x)/(a||1)+(s?s[0]-wt[0]:0)/(a||1),u=(i.clientY-it.clientY+o.y)/(l||1)+(s?s[1]-wt[1]:0)/(l||1);if(!kt.active&&!gt){if(n&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<n)return;this._onDragStart(t,!0)}if(G){r?(r.e+=c-(at||0),r.f+=u-(lt||0)):r={a:1,b:0,c:0,d:1,e:c,f:u};var d="matrix(".concat(r.a,",").concat(r.b,",").concat(r.c,",").concat(r.d,",").concat(r.e,",").concat(r.f,")");w(G,"webkitTransform",d),w(G,"mozTransform",d),w(G,"msTransform",d),w(G,"transform",d),at=c,lt=u,rt=i}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!G){var t=this.options.fallbackOnBody?document.body:U,e=_(W,!0,_t,!0,t),n=this.options;if(_t){for(pt=t;"static"===w(pt,"position")&&"none"===w(pt,"transform")&&pt!==document;)pt=pt.parentNode;pt!==document.body&&pt!==document.documentElement?(pt===document&&(pt=S()),e.top+=pt.scrollTop,e.left+=pt.scrollLeft):pt=S(),wt=O(pt)}y(G=W.cloneNode(!0),n.ghostClass,!1),y(G,n.fallbackClass,!0),y(G,n.dragClass,!0),w(G,"transition",""),w(G,"transform",""),w(G,"box-sizing","border-box"),w(G,"margin",0),w(G,"top",e.top),w(G,"left",e.left),w(G,"width",e.width),w(G,"height",e.height),w(G,"opacity","0.8"),w(G,"position",_t?"absolute":"fixed"),w(G,"zIndex","100000"),w(G,"pointerEvents","none"),kt.ghost=G,t.appendChild(G),w(G,"transform-origin",st/parseInt(G.style.width)*100+"% "+ct/parseInt(G.style.height)*100+"%")}},_onDragStart:function(t,e){var n=this,o=t.dataTransfer,i=n.options;j("dragStart",this,{evt:t}),kt.eventCanceled?this._onDrop():(j("setupClone",this),kt.eventCanceled||((Z=k(W)).draggable=!1,Z.style["will-change"]="",this._hideClone(),y(Z,this.options.chosenClass,!1),kt.clone=Z),n.cloneId=Ft((function(){j("clone",n),kt.eventCanceled||(n.options.removeCloneOnHide||U.insertBefore(Z,W),n._hideClone(),K({sortable:n,name:"clone"}))})),e||y(W,i.dragClass,!0),e?(vt=!0,n._loopId=setInterval(n._emulateDragOver,50)):(p(document,"mouseup",n._onDrop),p(document,"touchend",n._onDrop),p(document,"touchcancel",n._onDrop),o&&(o.effectAllowed="move",i.setData&&i.setData.call(n,o,W)),f(document,"drop",n),w(W,"transform","translateZ(0)")),gt=!0,n._dragStartId=Ft(n._dragStarted.bind(n,e,t)),f(document,"selectstart",n),ut=!0,c&&w(document.body,"user-select","none"))},_onDragOver:function(t){var e,o,i,r,a=this.el,l=t.target,s=this.options,c=s.group,u=kt.active,d=nt===c,h=s.sort,f=ot||u,p=this,g=!1;if(!Et){if(void 0!==t.preventDefault&&t.cancelable&&t.preventDefault(),l=v(l,s.draggable,a,!0),B("dragOver"),kt.eventCanceled)return g;if(W.contains(t.target)||l.animated&&l.animatingX&&l.animatingY||p._ignoreWhileAnimating===l)return H(!1);if(vt=!1,u&&!s.disabled&&(d?h||(i=!U.contains(W)):ot===this||(this.lastPutMode=nt.checkPull(this,u,W,t))&&c.checkPut(this,u,W,t))){if(r="vertical"===this._getDirection(t,l),e=_(W),B("dragOverValid"),kt.eventCanceled)return g;if(i)return z=U,F(),this._hideClone(),B("revert"),kt.eventCanceled||(q?U.insertBefore(W,q):U.appendChild(W)),H(!0);var m=x(a,s.draggable);if(!m||function(t,e,n){var o=_(x(n.el,n.options.draggable));return e?t.clientX>o.right+10||t.clientX<=o.right&&t.clientY>o.bottom&&t.clientX>=o.left:t.clientX>o.right&&t.clientY>o.top||t.clientX<=o.right&&t.clientY>o.bottom+10}(t,r,this)&&!m.animated){if(m===W)return H(!1);if(m&&a===t.target&&(l=m),l&&(o=_(l)),!1!==Rt(U,a,W,e,l,o,t,!!l))return F(),a.appendChild(W),z=a,L(),H(!0)}else if(l.parentNode===a){o=_(l);var b,E,D,S=W.parentNode!==a,T=!function(t,e,n){var o=n?t.left:t.top,i=n?t.right:t.bottom,r=n?t.width:t.height,a=n?e.left:e.top,l=n?e.right:e.bottom,s=n?e.width:e.height;return o===a||i===l||o+r/2===a+s/2}(W.animated&&W.toRect||e,l.animated&&l.toRect||o,r),O=r?"top":"left",N=C(l,"top","top")||C(W,"top","top"),A=N?N.scrollTop:void 0;if(dt!==l&&(E=o[O],bt=!1,yt=!T&&s.invertSwap||S),0!==(b=function(t,e,n,o,i,r,a,l){var s=o?t.clientY:t.clientX,c=o?n.height:n.width,u=o?n.top:n.left,d=o?n.bottom:n.right,h=!1;if(!a)if(l&&ft<c*i){if(!bt&&(1===ht?u+c*r/2<s:s<d-c*r/2)&&(bt=!0),bt)h=!0;else if(1===ht?s<u+ft:d-ft<s)return-ht}else if(u+c*(1-i)/2<s&&s<d-c*(1-i)/2)return function(t){return M(W)<M(t)?1:-1}(e);return(h=h||a)&&(s<u+c*r/2||d-c*r/2<s)?u+c/2<s?1:-1:0}(t,l,o,r,T?1:s.swapThreshold,null==s.invertedSwapThreshold?s.swapThreshold:s.invertedSwapThreshold,yt,dt===l)))for(var I=M(W);I-=b,(D=z.children[I])&&("none"===w(D,"display")||D===G););if(0===b||D===l)return H(!1);ht=b;var k=(dt=l).nextElementSibling,R=!1,X=Rt(U,a,W,e,l,o,t,R=1===b);if(!1!==X)return 1!==X&&-1!==X||(R=1===X),Et=!0,setTimeout(Yt,30),F(),R&&!k?a.appendChild(W):l.parentNode.insertBefore(W,R?k:l),N&&P(N,0,A-N.scrollTop),z=W.parentNode,void 0===E||yt||(ft=Math.abs(E-_(l)[O])),L(),H(!0)}if(a.contains(W))return H(!1)}return!1}function B(s,c){j(s,p,n({evt:t,isOwner:d,axis:r?"vertical":"horizontal",revert:i,dragRect:e,targetRect:o,canSort:h,fromSortable:f,target:l,completed:H,onMove:function(n,o){return Rt(U,a,W,e,n,_(n),t,o)},changed:L},c))}function F(){B("dragOverAnimationCapture"),p.captureAnimationState(),p!==f&&f.captureAnimationState()}function H(e){return B("dragOverCompleted",{insertion:e}),e&&(d?u._hideClone():u._showClone(p),p!==f&&(y(W,ot?ot.options.ghostClass:u.options.ghostClass,!1),y(W,s.ghostClass,!0)),ot!==p&&p!==kt.active?ot=p:p===kt.active&&ot&&(ot=null),f===p&&(p._ignoreWhileAnimating=l),p.animateAll((function(){B("dragOverAnimationComplete"),p._ignoreWhileAnimating=null})),p!==f&&(f.animateAll(),f._ignoreWhileAnimating=null)),(l===W&&!W.animated||l===a&&!l.animated)&&(dt=null),s.dragoverBubble||t.rootEl||l===document||(W.parentNode[Y]._isOutsideThisEl(t.target),e||It(t)),!s.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),g=!0}function L(){J=M(W),et=M(W,s.draggable),K({sortable:p,name:"change",toEl:a,newIndex:J,newDraggableIndex:et,originalEvent:t})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){p(document,"mousemove",this._onTouchMove),p(document,"touchmove",this._onTouchMove),p(document,"pointermove",this._onTouchMove),p(document,"dragover",It),p(document,"mousemove",It),p(document,"touchmove",It)},_offUpEvents:function(){var t=this.el.ownerDocument;p(t,"mouseup",this._onDrop),p(t,"touchend",this._onDrop),p(t,"pointerup",this._onDrop),p(t,"touchcancel",this._onDrop),p(document,"selectstart",this)},_onDrop:function(t){var e=this.el,n=this.options;J=M(W),et=M(W,n.draggable),j("drop",this,{evt:t}),z=W&&W.parentNode,J=M(W),et=M(W,n.draggable),kt.eventCanceled||(bt=yt=gt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Ht(this.cloneId),Ht(this._dragStartId),this.nativeDraggable&&(p(document,"drop",this),p(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),c&&w(document.body,"user-select",""),w(W,"transform",""),t&&(ut&&(t.cancelable&&t.preventDefault(),n.dropBubble||t.stopPropagation()),G&&G.parentNode&&G.parentNode.removeChild(G),(U===z||ot&&"clone"!==ot.lastPutMode)&&Z&&Z.parentNode&&Z.parentNode.removeChild(Z),W&&(this.nativeDraggable&&p(W,"dragend",this),Xt(W),W.style["will-change"]="",ut&&!gt&&y(W,ot?ot.options.ghostClass:this.options.ghostClass,!1),y(W,this.options.chosenClass,!1),K({sortable:this,name:"unchoose",toEl:z,newIndex:null,newDraggableIndex:null,originalEvent:t}),U!==z?(0<=J&&(K({rootEl:z,name:"add",toEl:z,fromEl:U,originalEvent:t}),K({sortable:this,name:"remove",toEl:z,originalEvent:t}),K({rootEl:z,name:"sort",toEl:z,fromEl:U,originalEvent:t}),K({sortable:this,name:"sort",toEl:z,originalEvent:t})),ot&&ot.save()):J!==$&&0<=J&&(K({sortable:this,name:"update",toEl:z,originalEvent:t}),K({sortable:this,name:"sort",toEl:z,originalEvent:t})),kt.active&&(null!=J&&-1!==J||(J=$,et=tt),K({sortable:this,name:"end",toEl:z,originalEvent:t}),this.save())))),this._nulling()},_nulling:function(){j("nulling",this),U=W=z=G=q=Z=V=Q=it=rt=ut=J=et=$=tt=dt=ht=ot=nt=kt.dragged=kt.ghost=kt.clone=kt.active=null,Dt.forEach((function(t){t.checked=!0})),Dt.length=at=lt=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":W&&(this._onDragOver(t),function(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}(t));break;case"selectstart":t.preventDefault()}},toArray:function(){for(var t,e=[],n=this.el.children,o=0,i=n.length,r=this.options;o<i;o++)v(t=n[o],r.draggable,this.el,!1)&&e.push(t.getAttribute(r.dataIdAttr)||Bt(t));return e},sort:function(t,e){var n={},o=this.el;this.toArray().forEach((function(t,e){var i=o.children[e];v(i,this.options.draggable,o,!1)&&(n[t]=i)}),this),e&&this.captureAnimationState(),t.forEach((function(t){n[t]&&(o.removeChild(n[t]),o.appendChild(n[t]))})),e&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return v(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var n=this.options;if(void 0===e)return n[t];var o=H.modifyOption(this,t,e);n[t]=void 0!==o?o:e,"group"===t&&Ot(n)},destroy:function(){j("destroy",this);var t=this.el;t[Y]=null,p(t,"mousedown",this._onTapStart),p(t,"touchstart",this._onTapStart),p(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(p(t,"dragover",this),p(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),(function(t){t.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),mt.splice(mt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!Q){if(j("hideClone",this),kt.eventCanceled)return;w(Z,"display","none"),this.options.removeCloneOnHide&&Z.parentNode&&Z.parentNode.removeChild(Z),Q=!0}},_showClone:function(t){if("clone"===t.lastPutMode){if(Q){if(j("showClone",this),kt.eventCanceled)return;W.parentNode!=U||this.options.group.revertClone?q?U.insertBefore(Z,q):U.appendChild(Z):U.insertBefore(Z,W),this.options.group.revertClone&&this.animate(W,Z),w(Z,"display",""),Q=!1}}else this._hideClone()}},St&&f(document,"touchmove",(function(t){(kt.active||gt)&&t.cancelable&&t.preventDefault()})),kt.utils={on:f,off:p,css:w,find:D,is:function(t,e){return!!v(t,e,t,!1)},extend:function(t,e){if(t&&e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},throttle:I,closest:v,toggleClass:y,clone:k,index:M,nextTick:Ft,cancelNextTick:Ht,detectDirection:Mt,getChild:T},kt.get=function(t){return t[Y]},kt.mount=function(){for(var t=arguments.length,e=new Array(t),o=0;o<t;o++)e[o]=arguments[o];e[0].constructor===Array&&(e=e[0]),e.forEach((function(t){if(!t.prototype||!t.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(t));t.utils&&(kt.utils=n({},kt.utils,t.utils)),H.mount(t)}))},kt.create=function(t,e){return new kt(t,e)};var Lt,jt,Kt,Wt,zt,Gt,Ut=[],qt=!(kt.version="1.13.0");function Vt(){Ut.forEach((function(t){clearInterval(t.pid)})),Ut=[]}function Zt(){clearInterval(Gt)}function Qt(t){var e=t.originalEvent,n=t.putSortable,o=t.dragEl,i=t.activeSortable,r=t.dispatchSortableEvent,a=t.hideGhostForTarget,l=t.unhideGhostForTarget;if(e){var s=n||i;a();var c=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e,u=document.elementFromPoint(c.clientX,c.clientY);l(),s&&!s.el.contains(u)&&(r("spill"),this.onSpill({dragEl:o,putSortable:n}))}}var $t,Jt=I((function(t,e,n,o){if(e.scroll){var i,r=(t.touches?t.touches[0]:t).clientX,a=(t.touches?t.touches[0]:t).clientY,l=e.scrollSensitivity,s=e.scrollSpeed,c=S(),u=!1;jt!==n&&(jt=n,Vt(),Lt=e.scroll,i=e.scrollFn,!0===Lt&&(Lt=N(n,!0)));var d=0,h=Lt;do{var f,p=h,g=_(p),v=g.top,m=g.bottom,b=g.left,y=g.right,E=g.width,D=g.height,C=void 0,T=p.scrollWidth,x=p.scrollHeight,M=w(p),O=p.scrollLeft,A=p.scrollTop;f=p===c?(C=E<T&&("auto"===M.overflowX||"scroll"===M.overflowX||"visible"===M.overflowX),D<x&&("auto"===M.overflowY||"scroll"===M.overflowY||"visible"===M.overflowY)):(C=E<T&&("auto"===M.overflowX||"scroll"===M.overflowX),D<x&&("auto"===M.overflowY||"scroll"===M.overflowY));var I=C&&(Math.abs(y-r)<=l&&O+E<T)-(Math.abs(b-r)<=l&&!!O),k=f&&(Math.abs(m-a)<=l&&A+D<x)-(Math.abs(v-a)<=l&&!!A);if(!Ut[d])for(var R=0;R<=d;R++)Ut[R]||(Ut[R]={});Ut[d].vx==I&&Ut[d].vy==k&&Ut[d].el===p||(Ut[d].el=p,Ut[d].vx=I,Ut[d].vy=k,clearInterval(Ut[d].pid),0==I&&0==k||(u=!0,Ut[d].pid=setInterval(function(){o&&0===this.layer&&kt.active._onTouchMove(zt);var e=Ut[this.layer].vy?Ut[this.layer].vy*s:0,n=Ut[this.layer].vx?Ut[this.layer].vx*s:0;"function"==typeof i&&"continue"!==i.call(kt.dragged.parentNode[Y],n,e,t,zt,Ut[this.layer].el)||P(Ut[this.layer].el,n,e)}.bind({layer:d}),24))),d++}while(e.bubbleScroll&&h!==c&&(h=N(h,!1)));qt=u}}),30);function te(){}function ee(){}te.prototype={startIndex:null,dragStart:function(t){var e=t.oldDraggableIndex;this.startIndex=e},onSpill:function(t){var e=t.dragEl,n=t.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var o=T(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(e,o):this.sortable.el.appendChild(e),this.sortable.animateAll(),n&&n.animateAll()},drop:Qt},e(te,{pluginName:"revertOnSpill"}),ee.prototype={onSpill:function(t){var e=t.dragEl,n=t.putSortable||this.sortable;n.captureAnimationState(),e.parentNode&&e.parentNode.removeChild(e),n.animateAll()},drop:Qt},e(ee,{pluginName:"removeOnSpill"});var ne,oe,ie,re,ae,le=[],se=[],ce=!1,ue=!1,de=!1;function he(t,e){se.forEach((function(n,o){var i=e.children[n.sortableIndex+(t?Number(o):0)];i?e.insertBefore(n,i):e.appendChild(n)}))}function fe(){le.forEach((function(t){t!==ie&&t.parentNode&&t.parentNode.removeChild(t)}))}return kt.mount(new function(){function t(){for(var t in this.defaults={scroll:!0,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===t.charAt(0)&&"function"==typeof this[t]&&(this[t]=this[t].bind(this))}return t.prototype={dragStarted:function(t){var e=t.originalEvent;this.sortable.nativeDraggable?f(document,"dragover",this._handleAutoScroll):this.options.supportPointer?f(document,"pointermove",this._handleFallbackAutoScroll):e.touches?f(document,"touchmove",this._handleFallbackAutoScroll):f(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var e=t.originalEvent;this.options.dragOverBubble||e.rootEl||this._handleAutoScroll(e)},drop:function(){this.sortable.nativeDraggable?p(document,"dragover",this._handleAutoScroll):(p(document,"pointermove",this._handleFallbackAutoScroll),p(document,"touchmove",this._handleFallbackAutoScroll),p(document,"mousemove",this._handleFallbackAutoScroll)),Zt(),Vt(),clearTimeout(m),m=void 0},nulling:function(){zt=jt=Lt=qt=Gt=Kt=Wt=null,Ut.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,e){var n=this,o=(t.touches?t.touches[0]:t).clientX,i=(t.touches?t.touches[0]:t).clientY,r=document.elementFromPoint(o,i);if(zt=t,e||l||a||c){Jt(t,this.options,r,e);var s=N(r,!0);!qt||Gt&&o===Kt&&i===Wt||(Gt&&Zt(),Gt=setInterval((function(){var r=N(document.elementFromPoint(o,i),!0);r!==s&&(s=r,Vt()),Jt(t,n.options,r,e)}),10),Kt=o,Wt=i)}else{if(!this.options.bubbleScroll||N(r,!0)===S())return void Vt();Jt(t,this.options,N(r,!1),!1)}}},e(t,{pluginName:"scroll",initializeByDefault:!0})}),kt.mount(ee,te),kt.mount(new function(){function t(){this.defaults={swapClass:"sortable-swap-highlight"}}return t.prototype={dragStart:function(t){var e=t.dragEl;$t=e},dragOverValid:function(t){var e=t.completed,n=t.target,o=t.onMove,i=t.activeSortable,r=t.changed,a=t.cancel;if(i.options.swap){var l=this.sortable.el,s=this.options;if(n&&n!==l){var c=$t;$t=!1!==o(n)?(y(n,s.swapClass,!0),n):null,c&&c!==$t&&y(c,s.swapClass,!1)}r(),e(!0),a()}},drop:function(t){var e=t.activeSortable,n=t.putSortable,o=t.dragEl,i=n||this.sortable,r=this.options;$t&&y($t,r.swapClass,!1),$t&&(r.swap||n&&n.options.swap)&&o!==$t&&(i.captureAnimationState(),i!==e&&e.captureAnimationState(),function(t,e){var n,o,i=t.parentNode,r=e.parentNode;i&&r&&!i.isEqualNode(e)&&!r.isEqualNode(t)&&(n=M(t),o=M(e),i.isEqualNode(r)&&n<o&&o++,i.insertBefore(e,i.children[n]),r.insertBefore(t,r.children[o]))}(o,$t),i.animateAll(),i!==e&&e.animateAll())},nulling:function(){$t=null}},e(t,{pluginName:"swap",eventProperties:function(){return{swapItem:$t}}})}),kt.mount(new function(){function t(t){for(var e in this)"_"===e.charAt(0)&&"function"==typeof this[e]&&(this[e]=this[e].bind(this));t.options.supportPointer?f(document,"pointerup",this._deselectMultiDrag):(f(document,"mouseup",this._deselectMultiDrag),f(document,"touchend",this._deselectMultiDrag)),f(document,"keydown",this._checkKeyDown),f(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,setData:function(e,n){var o="";le.length&&oe===t?le.forEach((function(t,e){o+=(e?", ":"")+t.textContent})):o=n.textContent,e.setData("Text",o)}}}return t.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(t){var e=t.dragEl;ie=e},delayEnded:function(){this.isMultiDrag=~le.indexOf(ie)},setupClone:function(t){var e=t.sortable,n=t.cancel;if(this.isMultiDrag){for(var o=0;o<le.length;o++)se.push(k(le[o])),se[o].sortableIndex=le[o].sortableIndex,se[o].draggable=!1,se[o].style["will-change"]="",y(se[o],this.options.selectedClass,!1),le[o]===ie&&y(se[o],this.options.chosenClass,!1);e._hideClone(),n()}},clone:function(t){var e=t.sortable,n=t.rootEl,o=t.dispatchSortableEvent,i=t.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||le.length&&oe===e&&(he(!0,n),o("clone"),i()))},showClone:function(t){var e=t.cloneNowShown,n=t.rootEl,o=t.cancel;this.isMultiDrag&&(he(!1,n),se.forEach((function(t){w(t,"display","")})),e(),ae=!1,o())},hideClone:function(t){var e=this,n=(t.sortable,t.cloneNowHidden),o=t.cancel;this.isMultiDrag&&(se.forEach((function(t){w(t,"display","none"),e.options.removeCloneOnHide&&t.parentNode&&t.parentNode.removeChild(t)})),n(),ae=!0,o())},dragStartGlobal:function(t){t.sortable,!this.isMultiDrag&&oe&&oe.multiDrag._deselectMultiDrag(),le.forEach((function(t){t.sortableIndex=M(t)})),le=le.sort((function(t,e){return t.sortableIndex-e.sortableIndex})),de=!0},dragStarted:function(t){var e=this,n=t.sortable;if(this.isMultiDrag){if(this.options.sort&&(n.captureAnimationState(),this.options.animation)){le.forEach((function(t){t!==ie&&w(t,"position","absolute")}));var o=_(ie,!1,!0,!0);le.forEach((function(t){t!==ie&&R(t,o)})),ce=ue=!0}n.animateAll((function(){ce=ue=!1,e.options.animation&&le.forEach((function(t){X(t)})),e.options.sort&&fe()}))}},dragOver:function(t){var e=t.target,n=t.completed,o=t.cancel;ue&&~le.indexOf(e)&&(n(!1),o())},revert:function(t){var e=t.fromSortable,n=t.rootEl,o=t.sortable,i=t.dragRect;1<le.length&&(le.forEach((function(t){o.addAnimationState({target:t,rect:ue?_(t):i}),X(t),t.fromRect=i,e.removeAnimationState(t)})),ue=!1,function(t,e){le.forEach((function(n,o){var i=e.children[n.sortableIndex+(t?Number(o):0)];i?e.insertBefore(n,i):e.appendChild(n)}))}(!this.options.removeCloneOnHide,n))},dragOverCompleted:function(t){var e=t.sortable,n=t.isOwner,o=t.insertion,i=t.activeSortable,r=t.parentEl,a=t.putSortable,l=this.options;if(o){if(n&&i._hideClone(),ce=!1,l.animation&&1<le.length&&(ue||!n&&!i.options.sort&&!a)){var s=_(ie,!1,!0,!0);le.forEach((function(t){t!==ie&&(R(t,s),r.appendChild(t))})),ue=!0}if(!n)if(ue||fe(),1<le.length){var c=ae;i._showClone(e),i.options.animation&&!ae&&c&&se.forEach((function(t){i.addAnimationState({target:t,rect:re}),t.fromRect=re,t.thisAnimationDuration=null}))}else i._showClone(e)}},dragOverAnimationCapture:function(t){var n=t.dragRect,o=t.isOwner,i=t.activeSortable;if(le.forEach((function(t){t.thisAnimationDuration=null})),i.options.animation&&!o&&i.multiDrag.isMultiDrag){re=e({},n);var r=E(ie,!0);re.top-=r.f,re.left-=r.e}},dragOverAnimationComplete:function(){ue&&(ue=!1,fe())},drop:function(t){var e=t.originalEvent,n=t.rootEl,o=t.parentEl,i=t.sortable,r=t.dispatchSortableEvent,a=t.oldIndex,l=t.putSortable,s=l||this.sortable;if(e){var c=this.options,u=o.children;if(!de)if(c.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),y(ie,c.selectedClass,!~le.indexOf(ie)),~le.indexOf(ie))le.splice(le.indexOf(ie),1),ne=null,L({sortable:i,rootEl:n,name:"deselect",targetEl:ie,originalEvt:e});else{if(le.push(ie),L({sortable:i,rootEl:n,name:"select",targetEl:ie,originalEvt:e}),e.shiftKey&&ne&&i.el.contains(ne)){var d,h,f=M(ne),p=M(ie);if(~f&&~p&&f!==p)for(d=f<p?(h=f,p):(h=p,f+1);h<d;h++)~le.indexOf(u[h])||(y(u[h],c.selectedClass,!0),le.push(u[h]),L({sortable:i,rootEl:n,name:"select",targetEl:u[h],originalEvt:e}))}else ne=ie;oe=s}if(de&&this.isMultiDrag){if((o[Y].options.sort||o!==n)&&1<le.length){var g=_(ie),v=M(ie,":not(."+this.options.selectedClass+")");if(!ce&&c.animation&&(ie.thisAnimationDuration=null),s.captureAnimationState(),!ce&&(c.animation&&(ie.fromRect=g,le.forEach((function(t){if(t.thisAnimationDuration=null,t!==ie){var e=ue?_(t):g;t.fromRect=e,s.addAnimationState({target:t,rect:e})}}))),fe(),le.forEach((function(t){u[v]?o.insertBefore(t,u[v]):o.appendChild(t),v++})),a===M(ie))){var m=!1;le.forEach((function(t){t.sortableIndex===M(t)||(m=!0)})),m&&r("update")}le.forEach((function(t){X(t)})),s.animateAll()}oe=s}(n===o||l&&"clone"!==l.lastPutMode)&&se.forEach((function(t){t.parentNode&&t.parentNode.removeChild(t)}))}},nullingGlobal:function(){this.isMultiDrag=de=!1,se.length=0},destroyGlobal:function(){this._deselectMultiDrag(),p(document,"pointerup",this._deselectMultiDrag),p(document,"mouseup",this._deselectMultiDrag),p(document,"touchend",this._deselectMultiDrag),p(document,"keydown",this._checkKeyDown),p(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(t){if(!(void 0!==de&&de||oe!==this.sortable||t&&v(t.target,this.options.draggable,this.sortable.el,!1)||t&&0!==t.button))for(;le.length;){var e=le[0];y(e,this.options.selectedClass,!1),le.shift(),L({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:e,originalEvt:t})}},_checkKeyDown:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},e(t,{pluginName:"multiDrag",utils:{select:function(t){var e=t.parentNode[Y];e&&e.options.multiDrag&&!~le.indexOf(t)&&(oe&&oe!==e&&(oe.multiDrag._deselectMultiDrag(),oe=e),y(t,e.options.selectedClass,!0),le.push(t))},deselect:function(t){var e=t.parentNode[Y],n=le.indexOf(t);e&&e.options.multiDrag&&~n&&(y(t,e.options.selectedClass,!1),le.splice(n,1))}},eventProperties:function(){var t=this,e=[],n=[];return le.forEach((function(o){var i;e.push({multiDragElement:o,index:o.sortableIndex}),i=ue&&o!==ie?-1:ue?M(o,":not(."+t.options.selectedClass+")"):M(o),n.push({multiDragElement:o,index:i})})),{items:i(le),clones:[].concat(se),oldIndicies:e,newIndicies:n}},optionListeners:{multiDragKey:function(t){return"ctrl"===(t=t.toLowerCase())?t="Control":1<t.length&&(t=t.charAt(0).toUpperCase()+t.substr(1)),t}}})}),kt}));