<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'reference' => 'e491bd5bcf7d6e451c4407eafff131e2eba1c077',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => 'e491bd5bcf7d6e451c4407eafff131e2eba1c077',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hybridauth/hybridauth' => array(
            'pretty_version' => 'v3.11.0',
            'version' => '3.11.0.0',
            'reference' => 'b70b9cbe1c7fa99b5847c5868610a8f024cc7d38',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hybridauth/hybridauth',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'df1e7fde177501eee2037dd159cf04f5f301a512',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpseclib/phpseclib' => array(
            'pretty_version' => '3.0.41',
            'version' => '3.0.41.0',
            'reference' => '621c73f7dcb310b61de34d1da4c4204e8ace6ceb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpseclib/phpseclib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
